<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cache Performance Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .results {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .api-counter {
            font-size: 18px;
            font-weight: bold;
            color: #dc3545;
            margin: 10px 0;
        }
        .success {
            color: #28a745;
        }
        .warning {
            color: #ffc107;
        }
        .error {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Pokemon Spawner Cache Performance Test</h1>
        
        <div class="api-counter">
            API Calls Made: <span id="apiCallCount">0</span>
        </div>
        
        <div class="test-section">
            <h3>Test 1: Cache Loading</h3>
            <p>Test if the landuse cache is properly loaded and shared between spawn methods.</p>
            <button class="test-button" onclick="testCacheLoading()">Test Cache Loading</button>
            <div id="cacheResults" class="results"></div>
        </div>
        
        <div class="test-section">
            <h3>Test 2: Spawn Performance</h3>
            <p>Test spawning 40 Pokemon and count API calls. Should be 1 call instead of 21.</p>
            <button class="test-button" onclick="testSpawnPerformance()">Test Spawn Performance</button>
            <div id="spawnResults" class="results"></div>
        </div>
        
        <div class="test-section">
            <h3>Test 3: Point-in-Polygon Lookup</h3>
            <p>Test the fast point-in-polygon lookup using cached data.</p>
            <button class="test-button" onclick="testPointLookup()">Test Point Lookup</button>
            <div id="lookupResults" class="results"></div>
        </div>

        <div class="test-section">
            <h3>Test 4: Reset Functionality</h3>
            <p>Test the clearAllSpawnsAndRespawn() function to verify cache optimization works after reset.</p>
            <button class="test-button" onclick="testResetFunctionality()">Test Reset Function</button>
            <div id="resetResults" class="results"></div>
        </div>

        <div class="test-section">
            <h3>API Call Monitor</h3>
            <p>Monitor all Overpass API calls made during testing.</p>
            <button class="test-button" onclick="clearApiLog()">Clear Log</button>
            <div id="apiLog" class="results"></div>
        </div>
    </div>

    <script src="../lib/turf.min.js"></script>
    <script type="module">
        import { PokemonSpawner } from '../services/pokemon-spawner.js';
        import { gameState } from '../state/game-state.js';
        
        // API call monitoring
        let apiCallCount = 0;
        let apiLog = [];
        
        // Override fetch to monitor API calls
        const originalFetch = window.fetch;
        window.fetch = function(...args) {
            const url = args[0];
            if (typeof url === 'string' && url.includes('overpass-api.de')) {
                apiCallCount++;
                const timestamp = new Date().toLocaleTimeString();
                apiLog.push(`[${timestamp}] API Call #${apiCallCount}: ${url}`);
                document.getElementById('apiCallCount').textContent = apiCallCount;
                updateApiLog();
            }
            return originalFetch.apply(this, args);
        };
        
        function updateApiLog() {
            document.getElementById('apiLog').textContent = apiLog.join('\n');
        }
        
        window.clearApiLog = function() {
            apiCallCount = 0;
            apiLog = [];
            document.getElementById('apiCallCount').textContent = '0';
            document.getElementById('apiLog').textContent = '';
        };
        
        // Initialize game state with mock data
        async function initializeGameState() {
            try {
                const response = await fetch('../pokedex-151.json');
                gameState.pokedexData = await response.json();
                gameState.pokemons = [];
                gameState.pokemonMarkers = new Map();
                console.log('Game state initialized with', gameState.pokedexData.length, 'Pokemon');
            } catch (e) {
                console.error('Failed to initialize game state:', e);
            }
        }
        
        // Test cache loading
        window.testCacheLoading = async function() {
            const results = document.getElementById('cacheResults');
            results.textContent = 'Testing cache loading...\n';
            
            try {
                await initializeGameState();
                const spawner = new PokemonSpawner();
                
                // Test coordinates (Münster, Germany)
                const lat = 51.96;
                const lng = 7.62;
                const radius = 500;
                
                const startTime = Date.now();
                const geojson = await spawner.loadLanduseDataForArea(lat, lng, radius);
                const loadTime = Date.now() - startTime;
                
                results.textContent += `✓ Cache loaded in ${loadTime}ms\n`;
                results.textContent += `✓ Found ${geojson.features.length} landuse features\n`;
                
                // Test cache hit
                const startTime2 = Date.now();
                const geojson2 = await spawner.loadLanduseDataForArea(lat, lng, radius);
                const cacheTime = Date.now() - startTime2;
                
                results.textContent += `✓ Cache hit in ${cacheTime}ms (should be <10ms)\n`;
                
                if (cacheTime < 10) {
                    results.textContent += '✓ Cache is working correctly!\n';
                    results.className = 'results success';
                } else {
                    results.textContent += '⚠ Cache might not be working optimally\n';
                    results.className = 'results warning';
                }
                
            } catch (e) {
                results.textContent += `✗ Error: ${e.message}\n`;
                results.className = 'results error';
            }
        };
        
        // Test spawn performance
        window.testSpawnPerformance = async function() {
            const results = document.getElementById('spawnResults');
            results.textContent = 'Testing spawn performance...\n';

            try {
                await initializeGameState();
                const spawner = new PokemonSpawner();

                // Reset API call counter
                const initialApiCalls = apiCallCount;

                // Test coordinates
                const lat = 51.96;
                const lng = 7.62;

                results.textContent += 'Starting spawn test (20 random + 20 landuse special)...\n';

                const startTime = Date.now();

                // Spawn both types in parallel (like in main.js)
                const [randomSpawns, landuseSpawns] = await Promise.all([
                    spawner.spawnRandomPokemons(lat, lng, 20),
                    spawner.spawnLanduseSpecialPokemons(lat, lng, 20)
                ]);

                const totalTime = Date.now() - startTime;
                const apiCallsMade = apiCallCount - initialApiCalls;

                results.textContent += `✓ Spawned ${randomSpawns.length} random Pokemon\n`;
                results.textContent += `✓ Spawned ${landuseSpawns.length} landuse special Pokemon\n`;
                results.textContent += `✓ Total time: ${totalTime}ms\n`;
                results.textContent += `✓ API calls made: ${apiCallsMade}\n`;

                // Check landuse data in spawned Pokemon
                let randomWithLanduse = 0;
                let landuseWithLanduse = 0;

                randomSpawns.forEach(p => {
                    if (p.landuseTypeName) randomWithLanduse++;
                });

                landuseSpawns.forEach(p => {
                    if (p.landuseTypeName) landuseWithLanduse++;
                });

                results.textContent += `✓ Random Pokemon with landuse data: ${randomWithLanduse}/${randomSpawns.length}\n`;
                results.textContent += `✓ Landuse Pokemon with landuse data: ${landuseWithLanduse}/${landuseSpawns.length}\n`;

                if (apiCallsMade <= 1) {
                    results.textContent += '🎉 EXCELLENT! Cache optimization working perfectly!\n';
                    results.className = 'results success';
                } else if (apiCallsMade <= 5) {
                    results.textContent += '✓ Good! Significant improvement from cache\n';
                    results.className = 'results success';
                } else {
                    results.textContent += `⚠ Expected 1 API call, got ${apiCallsMade}. Cache may need optimization.\n`;
                    results.className = 'results warning';
                }

                if (randomWithLanduse === 0) {
                    results.textContent += '⚠ WARNING: Random Pokemon missing landuse data!\n';
                    results.className = 'results warning';
                }

            } catch (e) {
                results.textContent += `✗ Error: ${e.message}\n`;
                results.className = 'results error';
            }
        };
        
        // Test point lookup
        window.testPointLookup = async function() {
            const results = document.getElementById('lookupResults');
            results.textContent = 'Testing point-in-polygon lookup...\n';
            
            try {
                await initializeGameState();
                const spawner = new PokemonSpawner();
                
                // Load cache first
                const lat = 51.96;
                const lng = 7.62;
                await spawner.loadLanduseDataForArea(lat, lng, 500);
                
                // Test multiple points
                const testPoints = [
                    {lat: 51.96, lng: 7.62, name: 'Center'},
                    {lat: 51.961, lng: 7.621, name: 'Nearby 1'},
                    {lat: 51.959, lng: 7.619, name: 'Nearby 2'},
                    {lat: 51.965, lng: 7.625, name: 'Edge'},
                    {lat: 52.0, lng: 8.0, name: 'Far away'}
                ];
                
                for (const point of testPoints) {
                    const startTime = Date.now();
                    const landuse = spawner.getLanduseForPoint(point.lat, point.lng);
                    const lookupTime = Date.now() - startTime;
                    
                    if (landuse) {
                        results.textContent += `✓ ${point.name}: ${landuse.value} (${lookupTime}ms)\n`;
                    } else {
                        results.textContent += `- ${point.name}: No landuse found (${lookupTime}ms)\n`;
                    }
                }
                
                results.textContent += '\n✓ Point lookup test completed!\n';
                results.className = 'results success';
                
            } catch (e) {
                results.textContent += `✗ Error: ${e.message}\n`;
                results.className = 'results error';
            }
        };

        // Test reset functionality
        window.testResetFunctionality = async function() {
            const results = document.getElementById('resetResults');
            results.textContent = 'Testing reset functionality...\n';

            try {
                await initializeGameState();

                // Import FabManager to access reset function
                const { FabManager } = await import('../ui/FabManager.js');
                const fabManager = new FabManager();

                results.textContent += 'Calling clearAllSpawnsAndRespawn()...\n';

                // Reset API call counter
                const initialApiCalls = apiCallCount;

                // Call the reset function
                await fabManager.clearAllSpawnsAndRespawn();

                const apiCallsMade = apiCallCount - initialApiCalls;

                results.textContent += `✓ Reset completed\n`;
                results.textContent += `✓ API calls made during reset: ${apiCallsMade}\n`;
                results.textContent += `✓ Pokemon count after reset: ${gameState.pokemons.length}\n`;

                // Check landuse data in spawned Pokemon
                let randomWithLanduse = 0;
                let landuseWithLanduse = 0;

                gameState.pokemons.forEach(p => {
                    if (p.landuseSpecial) {
                        if (p.landuseTypeName) landuseWithLanduse++;
                    } else {
                        if (p.landuseTypeName) randomWithLanduse++;
                    }
                });

                results.textContent += `✓ Random Pokemon with landuse data: ${randomWithLanduse}\n`;
                results.textContent += `✓ Landuse Pokemon with landuse data: ${landuseWithLanduse}\n`;

                if (apiCallsMade <= 1) {
                    results.textContent += '🎉 EXCELLENT! Reset uses cache optimization!\n';
                    results.className = 'results success';
                } else {
                    results.textContent += `⚠ Expected 1 API call, got ${apiCallsMade}. Cache may not be working.\n`;
                    results.className = 'results warning';
                }

                if (randomWithLanduse === 0) {
                    results.textContent += '⚠ WARNING: Random Pokemon missing landuse data after reset!\n';
                    results.className = 'results warning';
                }

            } catch (e) {
                results.textContent += `✗ Error: ${e.message}\n`;
                results.className = 'results error';
            }
        };

    </script>
</body>
</html>
