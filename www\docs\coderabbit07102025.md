Prüfe folgende Verbesserungsvorschläge. Sind diese sinnvoll für unsere aktuelle codebase und gerechtfertigt, dann integriere sie oder passe sie gegebenenfalls so an, dass sie zu unserem bisherigen Code passen.

docs/localization_future.md:
1. Define the missing getFallbackTranslation method.

The t() method calls this.getFallbackTranslation(key) which is not defined in the class. This would cause a runtime error.

Add the missing method:

getFallbackTranslation(key) {
  const keys = key.split('.');
  let value = this.translations[this.fallbackLocale];
  
  for (const k of keys) {
    value = value?.[k];
  }
  
  return value || key; // Return key as last resort
}

2. Clarify inconsistency about battle message language.

This section states battle messages are "currently in English but should be German," which conflicts with the document's opening statement that the app uses "hardcoded German localization." Based on the earlier example (line 51), it appears battle messages are indeed in English.

Consider clarifying the current state more precisely:

 ### 2. Battle Messages
-Battle result messages in `battle-calc.js` are currently in English but should be German:
+Battle result messages in `battle-calc.js` are currently in English, inconsistent with the rest of the German UI:
This makes it clear that battle messages are an exception to the otherwise German localization.

3. Clarify the mixed language situation.

The comment suggests that "Victory!" should be translated to German ("Sieg!"), but the example code shows English text. This creates confusion about the current state. Consider clarifying whether this is:

An example of the current English code that needs German translation, or
An inconsistency in the current implementation
Apply this diff to clarify the intent:

-resultMessage = `Victory! ${wildPokemonDisplayName} has been defeated.`;
-// Should be: "Sieg! {pokemonName} wurde besiegt!"
+// Current: English text that should be German
+resultMessage = `Victory! ${wildPokemonDisplayName} has been defeated.`;
+// Expected German: "Sieg! {pokemonName} wurde besiegt!"

services/map-renderer.js:

1. Same orphaned markers issue as clearAllPokemonMarkers.

This method has the same issue where failed removals still get cleared from the tracking collection (Line 763). Additionally, this method is nearly identical to clearAllPokemonMarkers, suggesting an opportunity to reduce duplication.

Apply the same fix as suggested for clearAllPokemonMarkers, and consider extracting a shared helper method:

private _clearMarkerCollection(markerCollection, collectionName) {
  const { map } = gameState;
  const failedRemovals = [];

  try {
    markerCollection.forEach((entry, id) => {
      try {
        if (entry.marker && map) {
          map.removeLayer(entry.marker);
          markerCollection.delete(id);
        }
      } catch (e) {
        logger.error(`Error removing ${collectionName} marker ${id}:`, e);
        failedRemovals.push(id);
      }
    });

    logger.debug(`Removed ${markerCollection.size} ${collectionName} markers, ${failedRemovals.length} failed`);
  } catch (e) {
    logger.error(`Error clearing ${collectionName} markers:`, e);
  }
}

2. Consider tracking failed removals to prevent orphaned markers.

If map.removeLayer() throws an error for individual markers (caught and logged in the inner try-catch), those markers remain on the map but are still cleared from the pokemonMarkers collection on Line 737. This creates orphaned markers that cannot be tracked or removed later.

Consider only clearing successfully removed markers:

  clearAllPokemonMarkers() {
    const { pokemonMarkers, map } = gameState;
-   let removedCount = 0;
+   const failedRemovals = [];

    try {
      pokemonMarkers.forEach((entry, id) => {
        try {
          if (entry.marker && map) {
            map.removeLayer(entry.marker);
-           removedCount++;
+           pokemonMarkers.delete(id);
          }
        } catch (e) {
          logger.error(`Error removing Pokemon marker ${id}:`, e);
+         failedRemovals.push(id);
        }
      });

-     pokemonMarkers.clear();
-     logger.debug(`Removed ${removedCount} Pokemon markers from map`);
+     logger.debug(`Removed ${pokemonMarkers.size - failedRemovals.length} Pokemon markers, ${failedRemovals.length} failed`);
    } catch (e) {
      logger.error('Error clearing Pokemon markers:', e);
    }
  }

config.js:

1. Consider increasing positionThreshold to account for GPS accuracy limits.

A 2-meter threshold may be too sensitive for typical GPS accuracy, even with enableHighAccuracy: true. Standard GPS accuracy is 5-10m outdoors and worse indoors, which means GPS drift/jitter can easily exceed 2m and trigger false movement detection. This could lead to:

Constant false-positive movement events
Increased battery drain from excessive position updates
Degraded user experience
Consider increasing the threshold to 5-10 meters to better match real-world GPS accuracy and filter out noise.

Apply this diff to use a more robust threshold:

-    positionThreshold: 2          // Minimum distance (meters) to consider as actual movement
+    positionThreshold: 5          // Minimum distance (meters) to consider as actual movement

services/battle-calc.js:

1. Fix variable redeclaration error.

wildPokemonDisplayName is already declared at line 293. Redeclaring it here with const causes a syntax error.

Apply this diff to remove the redeclaration:

-      const wildPokemonDisplayName = getGermanPokemonName(wildPokemon);
-      resultMessage = `Test Victory! ${wildPokemonDisplayName} has been defeated.`;
+      resultMessage = `Test Victory! ${wildPokemonDisplayName} has been defeated.`;

ui/EncountersScreen.js:

1. Critical: Missing error state tracking for race condition.

If loadEncountersData fails before setContainer is called, the error state won't be rendered. Here's the problematic flow:

Constructor calls loadEncountersData (line 24) with container = null
Error occurs in loadEncountersData → isLoading = false (line 57)
renderErrorState is guarded by container check (lines 58-60) → error not rendered
Later, setContainer is called (line 516) → isLoading = false → else branch (lines 39-41) executes
render() displays an empty encounters list instead of the error
Add error state tracking to handle this race condition:

   constructor(container, options = {}) {
     super(container, options);
     this.encounters = options.encounters || [];
     this.isLoading = true;
+    this.hasError = false;
+    this.error = null;
     this.elements = {};
   setContainer(container) {
     this.container = container;
-    if (this.isLoading) {
+    if (this.hasError) {
+      this.renderErrorState(this.error);
+    } else if (this.isLoading) {
       this.renderLoadingState();
     } else {
       this.render();
       this.addEventListeners();
     }
   }
     } catch (e) {
       logger.error('Error loading encounters data:', e);
       this.isLoading = false;
+      this.hasError = true;
+      this.error = e;
       if (this.container) {
         this.renderErrorState(e);
       }
     }

ui/PokedexScreen.js:

1. Remove redundant check and guard recursive render() call.

Two issues in this data loading logic:

Redundant check (line 63): The condition !gameState.pokedexData || gameState.pokedexData.length === 0 duplicates the check on line 51, since pokedexData is assigned from gameState.pokedexData on line 48.

Unsafe recursive render() (line 58): The event listener calls this.render() without checking if the component is still mounted or if the container is valid. If the component is cleaned up before the event fires, this could cause errors or memory leaks.

Apply this diff to fix both issues:

       // If pokedex data is not loaded yet, wait for it or trigger loading
       if (!pokedexData || pokedexData.length === 0) {
         logger.debug('Pokedex data not available, waiting for gameState to load it');
 
         // Subscribe to pokedexDataLoaded event for re-render
         if (!this.pokedexDataLoadedListener) {
           this.pokedexDataLoadedListener = gameState.events.on('pokedexDataLoaded', () => {
             logger.debug('Pokedex data loaded event received, re-rendering');
-            this.render(); // Re-render when data arrives
+            // Only re-render if component is still mounted
+            if (this.container && this.container.isConnected) {
+              this.render();
+            }
           });
         }
 
-        // If gameState hasn't loaded data yet, trigger loading
-        if (!gameState.pokedexData || gameState.pokedexData.length === 0) {
-          await gameState.loadPokedexData();
-          pokedexData = gameState.pokedexData || [];
-        }
+        // Trigger loading since we already know data is missing
+        await gameState.loadPokedexData();
+        pokedexData = gameState.pokedexData || [];
       }

ui/TrainerBattleScreen.js:

1. Fix the trainer sprite cleanup selector.

Line 94 queries for .trainer-sprite-dynamic, but this class is never added to trainer sprites in the code. Looking at showTrainerIntroAnimation (lines 473-481), the sprites use classes like trainer-intro-sprite, player-trainer-sprite, and opponent-trainer-sprite.

Apply this diff to fix the selector:

-            // Remove dynamically added trainer sprites from battle background if present
-            const trainerSprites = this.container.querySelectorAll('.trainer-sprite-dynamic');
+            // Remove dynamically added trainer sprites from battle background if present
+            const trainerSprites = this.container.querySelectorAll('.trainer-intro-sprite');
             trainerSprites.forEach(sprite => {
                 if (sprite.parentNode) {
                     sprite.parentNode.removeChild(sprite);
                 }
             });

utils/overlay-manager.js:

1. Await async cleanup callbacks to prevent race conditions.

If the cleanup callback is asynchronous and returns a promise, it won't be awaited, potentially causing the overlay to close before cleanup completes. This could lead to incomplete state cleanup or race conditions.

Apply this diff to await the cleanup:

-      // Call cleanup callback if provided
-      if (cleanup && typeof cleanup === 'function') {
-        try {
-          cleanup();
-        } catch (e) {
-          logger.error('Error in overlay cleanup callback:', e);
-        }
-      }
+      // Call cleanup callback if provided
+      if (cleanup && typeof cleanup === 'function') {
+        try {
+          await cleanup();
+        } catch (e) {
+          logger.error('Error in overlay cleanup callback:', e);
+        }
+      }

