Anleitung: Spiel starten
🌐 Browser-Version starten
Terminal öffnen in  c:\Users\<USER>\CascadeProjects\gps-pokemon-app
HTTP-Server starten:
oder alternativ:
Browser öffnen:
Automatisch: http://127.0.0.1:8080/www/
Manuell: http://localhost:8080/www/
GPS-Simulation:
Browser wird nach Standort-Berechtigung fragen
Für Tests: F12 → Console → window.gpsMock.setMockLocation(51.96, 7.62)
📱 Android Studio Version
Capacitor synchronisieren:
npx cap sync android
Android Studio öffnen:
oder manuell: Android Studio → Open →  android/ Ordner auswählen
Gerät vorbereiten:
USB-Debugging aktivieren
Gerät per USB verbinden
In Android Studio: Gerät in der Geräteliste auswählen
App starten:
▶️ "Run" Button in Android Studio
Oder: Shift + F10
Berechtigungen erteilen:
GPS/Standort-Berechtigung erlauben
Kamera-Berechtigung (falls benötigt)
🔧 Troubleshooting
Browser:

HTTPS für GPS: npx http-server -S -p 8443 (mit SSL)
CORS-Probleme: npx http-server --cors
Android:

Build-Fehler: npx cap clean android && npx cap sync android
Gradle-Probleme: Android Studio → File → Invalidate Caches and Restart