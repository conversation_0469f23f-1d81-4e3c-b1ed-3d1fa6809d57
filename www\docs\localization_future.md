# Future Localization Implementation Guide

## Current State (Hardcoded German)

The application currently uses **hardcoded German localization** throughout the codebase. This document outlines the current implementation and provides guidance for implementing a proper multi-language localization system in the future.

---

## Current Localization Architecture

### 1. Pokémon Name Display

**Current Implementation:**
- **Internal Logic**: Uses English Pokémon names (e.g., "Pikachu", "Nidoran♂")
- **UI Display**: Uses German names from `pokedexData[].de` field
- **Utility Function**: `getGermanPokemonName()` in `www/utils/pokemon-display-names.js`

**Key Files:**
- `www/utils/pokemon-display-names.js` - Main utility for Pokémon name localization
- `www/services/battle-calc.js` - Battle result messages (lines 293-294, 391) - **Currently in English with German Pokemon names**
- `www/ui/BattleScreen.js` - Battle screen display
- `www/ui/TrainerBattleScreen.js` - Trainer battle display
- `www/utils/battle-utils.js` - Shared battle utilities (line 188)

**Data Source:**
- Pokémon names are stored in the pokedex data structure
- Each Pokémon entry has a `de` field for German names
- Fallback to English `name` field if German not available

**Current Language Inconsistency:**
- Pokemon names are displayed in German using `getGermanPokemonName()`
- However, battle result messages in `battle-calc.js` are in **English**:
  - Line 299: `"Victory! ${wildPokemonDisplayName} has been defeated."`
  - Line 304: `"Defeat! ${playerPokemonDisplayName} has lost the battle."`
  - Line 311-313: `"Close battle! ..."` messages
- This creates a mixed-language experience: English messages with German Pokemon names
- Example output: "Victory! Bisasam has been defeated." (English + German)

**Example Usage:**
```javascript
import { getGermanPokemonName } from './utils/pokemon-display-names.js';

const displayName = getGermanPokemonName(pokemon);
// Returns: "Pikachu" (German) or falls back to pokemon.name
```

---

### 2. UI Text and Messages

**Current Implementation:**
- All UI text is hardcoded in German directly in component files
- No centralized translation system
- Messages are embedded in HTML templates and JavaScript strings

**Examples of Hardcoded German Text:**

#### Battle Messages (`www/services/battle-calc.js`):
```javascript
// Current implementation (line 299 in battle-calc.js):
resultMessage = `Victory! ${wildPokemonDisplayName} has been defeated.`;
// Example output: "Victory! Bisasam has been defeated." (English message + German Pokemon name)

// For full German localization, this should be:
resultMessage = `Sieg! ${wildPokemonDisplayName} wurde besiegt!`;
// Example output: "Sieg! Bisasam wurde besiegt!" (German message + German Pokemon name)
```

#### Trainer Battle Screen (`www/ui/TrainerBattleScreen.js`):
```javascript
resultTextElement.innerHTML = `<span class="battle-victory">Sieg! Du hast ${result.npcTrainer.name} besiegt!</span>`;
resultTextElement.innerHTML = `<span class="battle-defeat">Niederlage! ${result.winner.name} hat dich besiegt!</span>`;
resultTextElement.innerHTML = `Runde ${status.currentRound + 1} - Pokémon bereit!`;
```

#### Button Labels:
- "Zurück" (Back)
- "Schließen" (Close)
- "Start Battle" (mixed English/German)
- "Weiter" (Continue)

#### Screen Headers:
- "Pokémon Battle"
- "Trainer Battle"
- Various status messages

---

## Files Requiring Localization

### High Priority (User-Facing Text)

1. **Battle System:**
   - `www/services/battle-calc.js` - Battle result messages
   - `www/ui/BattleScreen.js` - Battle screen UI
   - `www/ui/TrainerBattleScreen.js` - Trainer battle UI
   - `www/utils/battle-utils.js` - Shared battle utilities

2. **Pokémon Display:**
   - `www/utils/pokemon-display-names.js` - Name localization utility
   - `www/ui/PokemonCaughtScreen.js` - Caught Pokémon screen
   - `www/ui/PokedexScreen.js` - Pokédex display

3. **UI Components:**
   - All screen components in `www/ui/`
   - Button labels and tooltips
   - Error messages
   - Status notifications

### Medium Priority (System Messages)

4. **Game State & Services:**
   - `www/state/game-state.js` - Game state messages
   - `www/services/` - Various service messages
   - Logger messages (user-visible ones)

### Low Priority (Developer/Debug)

5. **Debug & Logging:**
   - Console log messages
   - Developer tools
   - Error stack traces

---

## Recommended Future Implementation

### Phase 1: Setup Localization Infrastructure

1. **Choose a Localization Library:**
   - **i18next** (recommended for JavaScript apps)
   - **vue-i18n** (if migrating to Vue.js)
   - **react-intl** (if migrating to React)
   - Custom lightweight solution

2. **Create Translation Files:**
   ```
   www/locales/
   ├── en.json          # English (base language)
   ├── de.json          # German (current default)
   ├── fr.json          # French (future)
   ├── es.json          # Spanish (future)
   └── ja.json          # Japanese (future)
   ```

3. **Translation File Structure:**
   ```json
   {
     "battle": {
       "victory": "Victory! {pokemonName} has been defeated.",
       "defeat": "Defeat! {pokemonName} has lost the battle.",
       "tie": "Close battle! {pokemonName} has been defeated in a tie.",
       "round": "Round {number} - Pokémon ready!",
       "startBattle": "Start Battle"
     },
     "buttons": {
       "back": "Back",
       "close": "Close",
       "continue": "Continue",
       "confirm": "Confirm"
     },
     "pokemon": {
       "level": "Lvl. {level}",
       "experience": "{current} / {max} XP",
       "levelUp": "{pokemonName} reached Level {level}!"
     }
   }
   ```

### Phase 2: Refactor Pokémon Name System

**Current:**
```javascript
export function getGermanPokemonName(pokemon) {
  // Returns German name from pokedexData[].de
}
```

**Future:**
```javascript
export function getPokemonName(pokemon, locale = 'en') {
  const pokedexEntry = findPokemonInPokedex(pokemon);

  // Support multiple languages
  const nameMap = {
    'en': pokedexEntry?.name,      // English
    'de': pokedexEntry?.de,         // German
    'fr': pokedexEntry?.fr,         // French
    'es': pokedexEntry?.es,         // Spanish
    'ja': pokedexEntry?.ja,         // Japanese
  };

  return nameMap[locale] || pokedexEntry?.name || 'Unknown';
}
```

**Pokedex Data Structure Enhancement:**
```json
{
  "dex_number": 25,
  "name": "Pikachu",
  "names": {
    "en": "Pikachu",
    "de": "Pikachu",
    "fr": "Pikachu",
    "es": "Pikachu",
    "ja": "ピカチュウ"
  }
}
```

### Phase 3: Implement Translation Service

**Create Translation Service:**
```javascript
// www/services/i18n-service.js
class I18nService {
  constructor() {
    this.currentLocale = 'de'; // Default to German (current behavior)
    this.translations = {};
    this.fallbackLocale = 'en';
  }

  async loadTranslations(locale) {
    const response = await fetch(`./locales/${locale}.json`);
    this.translations[locale] = await response.json();
  }

  t(key, params = {}) {
    const keys = key.split('.');
    let value = this.translations[this.currentLocale];

    for (const k of keys) {
      value = value?.[k];
    }

    // Fallback to English if translation missing
    if (!value) {
      value = this.getFallbackTranslation(key);
    }

    // Replace parameters
    return this.interpolate(value, params);
  }

  interpolate(text, params) {
    return text.replace(/\{(\w+)\}/g, (match, key) => params[key] || match);
  }

  // Fallback mechanism when translations are missing in the current locale
  getFallbackTranslation(key) {
    const keys = key.split('.');
    let value = this.translations[this.fallbackLocale];

    for (const k of keys) {
      value = value?.[k];
    }

    return value || `[Missing translation: ${key}]`;
  }

  setLocale(locale) {
    this.currentLocale = locale;
    localStorage.setItem('preferredLocale', locale);
  }

  getLocale() {
    return this.currentLocale;
  }
}

export const i18n = new I18nService();
```

**Usage Example:**
```javascript
import { i18n } from './services/i18n-service.js';

// Simple translation
const backButton = i18n.t('buttons.back'); // "Zurück" (German) or "Back" (English)

// With parameters
const victoryMessage = i18n.t('battle.victory', {
  pokemonName: getPokemonName(wildPokemon, i18n.getLocale())
});
// "Sieg! Pikachu wurde besiegt!" (German)
// "Victory! Pikachu has been defeated." (English)
```

### Phase 4: Refactor Components

**Before (Hardcoded German):**
```javascript
resultMessage = `Victory! ${wildPokemonDisplayName} has been defeated.`;
```

**After (Localized):**
```javascript
import { i18n } from '../services/i18n-service.js';
import { getPokemonName } from '../utils/pokemon-display-names.js';

const pokemonName = getPokemonName(wildPokemon, i18n.getLocale());
resultMessage = i18n.t('battle.victory', { pokemonName });
```

### Phase 5: Add Language Selector

**Settings Screen Addition:**
```javascript
<div class="settings-section">
  <h3>${i18n.t('settings.language')}</h3>
  <select id="language-selector">
    <option value="en">English</option>
    <option value="de">Deutsch</option>
    <option value="fr">Français</option>
    <option value="es">Español</option>
    <option value="ja">日本語</option>
  </select>
</div>
```

---

## Migration Strategy

### Step 1: Extract All Hardcoded Strings
1. Search codebase for German text patterns
2. Create comprehensive list of all UI strings
3. Categorize by priority and screen

### Step 2: Create Translation Files
1. Start with German (de.json) - extract current strings
2. Create English (en.json) as base language
3. Use translation service for additional languages

### Step 3: Implement Translation Service
1. Add i18n service
2. Load translations on app startup
3. Provide global access to translation function

### Step 4: Refactor Components (Incremental)
1. Start with high-priority screens (Battle, Pokédex)
2. Replace hardcoded strings with translation keys
3. Test thoroughly after each component
4. Maintain backward compatibility during migration

### Step 5: Update Pokémon Name System
1. Extend pokedex data with multiple language fields
2. Update `getPokemonName()` to support locale parameter
3. Refactor all calls to `getGermanPokemonName()`

### Step 6: Add Language Settings
1. Create settings screen with language selector
2. Persist language preference in localStorage
3. Reload translations when language changes
4. Update all UI components dynamically

---

## Testing Checklist

- [ ] All UI text displays correctly in each language
- [ ] Pokémon names display correctly in each language
- [ ] Battle messages use correct language
- [ ] Language switching works without page reload
- [ ] Language preference persists across sessions
- [ ] Fallback to English works when translation missing
- [ ] Special characters display correctly (ä, ö, ü, ♂, ♀, etc.)
- [ ] Text fits in UI elements (no overflow)
- [ ] Pluralization works correctly (if implemented)
- [ ] Date/time formatting respects locale
- [ ] Number formatting respects locale

---

## Known Issues & Considerations

### 1. Mixed Language Content

**Current State:** The application mixes English and German text throughout the UI.

**Specific Examples:**

1. **Battle Screen** (`www/ui/BattleScreen.js`):
   - Line 189: Header text `<h1>Pokémon Battle</h1>` (English)
   - Line 186-187: Back button `aria-label="Zurück"` and `alt="Zurück"` (German)
   - Line 257: Close button `alt="Schließen"` (German)
   - Battle messages from `battle-calc.js`: "Victory!", "Defeat!" (English)
   - Pokemon names: German via `getGermanPokemonName()`
   - **Result:** "Pokémon Battle" screen with "Zurück" button showing "Victory! Bisasam has been defeated."

2. **Trainer Battle Screen** (`www/ui/TrainerBattleScreen.js`):
   - Line 319: Header text `<h1>Trainerkampf</h1>` (German)
   - Line 316-317: Back button `aria-label="Zurück"` (German)
   - Line 792-794: Battle results "Sieg!", "Niederlage!" (German)
   - **Result:** Fully German UI (more consistent than Battle Screen)

3. **Other Screens:**
   - `PokedexScreen.js` line 106: `<h1>Pokédex</h1>` (International) with "Zurück" button (German)
   - `EncountersScreen.js` line 127: `<h1>Begegnungen</h1>` (German) with "Zurück" button (German)
   - `PokemonCaughtScreen.js` line 220: `<h1>Pokémon Team</h1>` (English/International) with "Zurück" button (German)

**Recommendation:** Standardize all UI text to a single language (German for current implementation, or make it configurable for future multi-language support).

### 2. Battle Messages Language Inconsistency
Battle result messages in `battle-calc.js` (lines 299, 304, 311-313) are currently in **English**, creating a mixed-language experience:
- Current: "Victory! Bisasam has been defeated." (English message + German Pokemon name)
- Current: "Defeat! Pikachu has lost the battle." (English message + German Pokemon name)
- Current: "Close battle! Bisasam has been defeated in a tie." (English message + German Pokemon name)

**For consistent German localization, these should be:**
- "Sieg! Bisasam wurde besiegt!"
- "Niederlage! Pikachu hat den Kampf verloren!"
- "Knappes Gefecht! Bisasam wurde in einem Unentschieden besiegt!"

**Note:** `TrainerBattleScreen.js` already uses German battle messages ("Sieg!", "Niederlage!"), showing inconsistency between wild Pokemon battles and trainer battles.

### 3. Pokémon Name Data
- Current pokedex only has `de` field for German names
- Need to add fields for other languages: `fr`, `es`, `ja`, etc.
- Consider using PokéAPI or similar for comprehensive language data

### 4. Dynamic Content
Some content is generated dynamically and needs special handling:
- Level-up notifications
- XP gain messages
- Battle calculations
- Error messages

### 5. Right-to-Left Languages
If supporting Arabic or Hebrew in future:
- Need RTL layout support
- Text alignment changes
- Icon positioning adjustments

---

## Resources

### Translation Data Sources
- **PokéAPI**: https://pokeapi.co/ (Pokémon names in multiple languages)
- **Bulbapedia**: https://bulbapedia.bulbagarden.net/ (Pokémon terminology)
- **Serebii**: https://www.serebii.net/ (Game text translations)

### Localization Libraries
- **i18next**: https://www.i18next.com/
- **Format.js**: https://formatjs.io/
- **Polyglot.js**: https://airbnb.io/polyglot.js/

### Best Practices
- Use translation keys, not English text as keys
- Keep translations in separate files, not in code
- Support pluralization and gender variations
- Consider cultural differences (colors, symbols, etc.)
- Test with native speakers
- Use professional translation services for quality

---

## Current Localization Points (Reference)

### Files Using `getGermanPokemonName()`:
1. `www/services/battle-calc.js` (lines 293-294, 391)
2. `www/ui/BattleScreen.js` (multiple locations)
3. `www/ui/TrainerBattleScreen.js` (multiple locations)
4. `www/utils/battle-utils.js` (line 188)
5. `www/ui/PokemonCaughtScreen.js`
6. `www/ui/PokedexScreen.js`
7. `www/ui/TeamScreen.js`

### Files with Hardcoded German Text:
1. `www/ui/TrainerBattleScreen.js`:
   - "Sieg! Du hast {name} besiegt!"
   - "Niederlage! {name} hat dich besiegt!"
   - "Unentschieden!"
   - "Runde {n} - Pokémon bereit!"
   - "Spieler gewinnt!"
   - "Gegner gewinnt!"

2. `www/ui/BattleScreen.js`:
   - "Zurück" button
   - "Pokémon Battle" header
   - Various status messages

3. Button labels across all screens:
   - "Zurück" (Back)
   - "Schließen" (Close)
   - "Weiter" (Continue)

---

## Conclusion

This document provides a comprehensive guide for implementing proper localization in the future. The current hardcoded German implementation works but limits the app to German-speaking users. Following this guide will enable multi-language support and improve accessibility for international users.

**Priority:** Medium (not critical for MVP, but important for wider adoption)

**Estimated Effort:** 2-3 weeks for full implementation
- Week 1: Setup infrastructure and create translation files
- Week 2: Refactor components and Pokémon name system
- Week 3: Testing, bug fixes, and polish

---

*Last Updated: 2025-10-06*
*Current Status: Hardcoded German localization*
*Future Goal: Multi-language support with i18n framework*
