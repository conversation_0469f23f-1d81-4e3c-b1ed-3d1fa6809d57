<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Battle Utils Consolidation Test</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            margin: 20px;
            background-color: #1a1a1a;
            color: #00ff00;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        h1 {
            color: #ffff00;
            text-align: center;
        }
        .test-output {
            background-color: #000;
            border: 1px solid #333;
            padding: 20px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-size: 14px;
            max-height: 600px;
            overflow-y: auto;
        }
        .run-button {
            background-color: #0066cc;
            color: white;
            border: none;
            padding: 10px 20px;
            font-size: 16px;
            border-radius: 5px;
            cursor: pointer;
            margin-bottom: 20px;
        }
        .run-button:hover {
            background-color: #0052a3;
        }
        .status {
            padding: 10px;
            margin-bottom: 20px;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success {
            background-color: #004d00;
            color: #00ff00;
        }
        .status.error {
            background-color: #4d0000;
            color: #ff0000;
        }
        .status.info {
            background-color: #003d4d;
            color: #00ccff;
        }
        .pokemon-exp-container {
            background-color: #333;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .pokemon-exp-bar {
            width: 100%;
            height: 20px;
            background-color: #666;
            border-radius: 10px;
            position: relative;
            overflow: hidden;
        }
        .pokemon-exp-fill {
            height: 100%;
            background-color: #4a90e2;
            border-radius: 10px;
            transition: width 0.5s ease;
        }
        .pokemon-exp-new {
            position: absolute;
            top: 0;
            height: 100%;
            background-color: #4a90e2;
            border-radius: 10px;
            opacity: 0;
            transition: all 0.3s ease;
        }
        .pokemon-exp-text {
            text-align: center;
            margin-top: 5px;
            font-size: 12px;
            color: #ccc;
        }
        .level-up-notification {
            background-color: #ffd700;
            color: #000;
            padding: 5px 10px;
            border-radius: 5px;
            margin-bottom: 10px;
            font-weight: bold;
            text-align: center;
        }
        .test-notification {
            background-color: #00ff00;
            color: #000;
            padding: 5px 10px;
            border-radius: 5px;
            margin-bottom: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Battle Utils Consolidation Test</h1>
        
        <div class="status info" id="status">
            Bereit zum Testen der konsolidierten Battle-Utility-Funktionen
        </div>
        
        <button class="run-button" onclick="runTests()">Tests starten</button>
        
        <div class="test-output" id="output">
            Klicke auf "Tests starten" um die Konsolidierungstests auszuführen...
        </div>
    </div>

    <script type="module">
        import { runAllTests } from './battle-utils-consolidation-test.js';
        
        // Override console methods to capture output
        const originalLog = console.log;
        const originalError = console.error;
        let testOutput = '';
        
        function captureConsole() {
            testOutput = '';
            console.log = function(...args) {
                testOutput += args.join(' ') + '\n';
                originalLog.apply(console, args);
            };
            console.error = function(...args) {
                testOutput += 'ERROR: ' + args.join(' ') + '\n';
                originalError.apply(console, args);
            };
        }
        
        function restoreConsole() {
            console.log = originalLog;
            console.error = originalError;
        }
        
        window.runTests = async function() {
            const statusEl = document.getElementById('status');
            const outputEl = document.getElementById('output');
            
            statusEl.textContent = 'Tests werden ausgeführt...';
            statusEl.className = 'status info';
            outputEl.textContent = 'Tests laufen...\n';
            
            try {
                captureConsole();
                await runAllTests();
                restoreConsole();
                
                outputEl.textContent = testOutput;
                
                if (testOutput.includes('❌')) {
                    statusEl.textContent = '⚠️ Einige Tests zeigten Probleme. Bitte Output prüfen.';
                    statusEl.className = 'status error';
                } else {
                    statusEl.textContent = '✅ Alle Tests erfolgreich! Battle-Utility-Funktionen sind konsolidiert.';
                    statusEl.className = 'status success';
                }
            } catch (error) {
                restoreConsole();
                statusEl.textContent = `❌ Fehler beim Ausführen der Tests: ${error.message}`;
                statusEl.className = 'status error';
                outputEl.textContent = `Fehler: ${error.message}\n${error.stack}`;
            }
        };
    </script>
</body>
</html>
