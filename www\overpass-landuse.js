// <PERSON>es Modul für Overpass-Landuse-Abfrage
// Nutzt fetch, gibt Promise<string> zurück (z.B. "forest", "residential", ... oder "unbekannt")

import { config } from './config.js';

// Enhanced cache system for Overpass API requests
const overpassCache = new Map();
const CACHE_DURATION_MS = 60 * 60 * 1000; // 60 minutes for 10km radius data
const MAX_CACHE_ENTRIES = 100; // Increased for larger radius data
const CACHE_KEY_PREFIX = 'overpass_landuse_';

// Enhanced cache key generation with radius and localStorage support
function generateCacheKey(lat, lng, radius) {
  // Runde auf 3 Dezimalstellen (~100m Genauigkeit) für bessere Cache-Hits
  const roundedLat = Math.round(lat * 1000) / 1000;
  const roundedLng = Math.round(lng * 1000) / 1000;
  return `${CACHE_KEY_PREFIX}${roundedLat}_${roundedLng}_${radius}`;
}

// Cache validation and expiration logic
function isValidCacheEntry(entry) {
  if (!entry || !entry.timestamp || !entry.data) return false;
  const age = Date.now() - entry.timestamp;
  return age < CACHE_DURATION_MS;
}

// Clear all Overpass cache data (memory and localStorage)
export function clearOverpassCache() {
  const memoryCacheSize = overpassCache.size;
  overpassCache.clear();

  // Also clear localStorage cache
  const localStorageKeys = [];
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key && key.startsWith(CACHE_KEY_PREFIX)) {
      localStorageKeys.push(key);
    }
  }

  localStorageKeys.forEach(key => localStorage.removeItem(key));

  console.log(`[OVERPASS_CACHE] 🗑️ Cleared ${memoryCacheSize} memory cache entries and ${localStorageKeys.length} localStorage entries`);
  return {
    memoryCacheCleared: memoryCacheSize,
    localStorageCacheCleared: localStorageKeys.length,
    totalCleared: memoryCacheSize + localStorageKeys.length
  };
}

// Enhanced cache retrieval with localStorage fallback
function getCachedResult(cacheKey) {
  // First check memory cache
  const memoryCache = overpassCache.get(cacheKey);
  if (memoryCache && isValidCacheEntry(memoryCache)) {
    console.log(`[OVERPASS-CACHE] 💾 Memory cache hit for: ${cacheKey}`);
    return memoryCache.data;
  }

  // Remove expired memory cache entry
  if (memoryCache) {
    overpassCache.delete(cacheKey);
    console.log(`[OVERPASS-CACHE] Expired memory cache entry removed: ${cacheKey}`);
  }

  // Fallback to localStorage cache
  try {
    const localStorageData = localStorage.getItem(cacheKey);
    if (localStorageData) {
      const parsed = JSON.parse(localStorageData);
      if (isValidCacheEntry(parsed)) {
        console.log(`[OVERPASS-CACHE] 💿 localStorage cache hit for: ${cacheKey}`);
        // Restore to memory cache for faster access
        overpassCache.set(cacheKey, parsed);
        return parsed.data;
      } else {
        // Remove expired localStorage entry
        localStorage.removeItem(cacheKey);
        console.log(`[OVERPASS-CACHE] Expired localStorage entry removed: ${cacheKey}`);
      }
    }
  } catch (e) {
    console.warn(`[OVERPASS-CACHE] ⚠️ Error reading localStorage cache: ${e.message}`);
  }

  return null;
}

// Enhanced cache storage with localStorage persistence
function setCachedResult(cacheKey, data) {
  const cacheEntry = {
    data: data,
    timestamp: Date.now()
  };

  // Store in memory cache
  if (overpassCache.size >= MAX_CACHE_ENTRIES) {
    const oldestKey = overpassCache.keys().next().value;
    overpassCache.delete(oldestKey);
    console.log(`[OVERPASS-CACHE] Removed oldest memory cache entry: ${oldestKey}`);
  }

  overpassCache.set(cacheKey, cacheEntry);

  // Also store in localStorage for persistence
  try {
    localStorage.setItem(cacheKey, JSON.stringify(cacheEntry));
    console.log(`[OVERPASS-CACHE] 💾 Cached result in memory and localStorage: ${cacheKey}`);
  } catch (e) {
    console.warn(`[OVERPASS-CACHE] ⚠️ Failed to store in localStorage: ${e.message}`);
    console.log(`[OVERPASS-CACHE] 💾 Cached result in memory only: ${cacheKey}`);
  }
}

// Hilfsfunktion: Punkt-in-Polygon-Test (Raycasting-Algorithmus)
function pointInPolygon(lat, lng, polygon) {
    let inside = false;
    for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
        const xi = polygon[i][0], yi = polygon[i][1];
        const xj = polygon[j][0], yj = polygon[j][1];
        const intersect = ((yi > lng) !== (yj > lng)) &&
            (lat < (xj - xi) * (lng - yi) / (yj - yi + 1e-10) + xi);
        if (intersect) inside = !inside;
    }
    return inside;
}

// Liefert alle Landuse/Natural/Leisure-Polygone im Umkreis als GeoJSON FeatureCollection
export async function getLandusePolygonsGeoJSON(lat, lng, radiusMeters = config.overpass.defaultRadius) {
    // Cache-Schlüssel generieren
    const cacheKey = generateCacheKey(lat, lng, radiusMeters);

    // Prüfen ob Ergebnis im Cache vorhanden ist
    const cachedResult = getCachedResult(cacheKey);
    if (cachedResult) {
        return cachedResult;
    }

    console.log(`[OVERPASS-CACHE] Cache miss, making API request for: ${cacheKey}`);

    const query = `[out:json][timeout:30];(
      way(around:${radiusMeters},${lat},${lng})[landuse];
      relation(around:${radiusMeters},${lat},${lng})[landuse];
      way(around:${radiusMeters},${lat},${lng})[natural];
      relation(around:${radiusMeters},${lat},${lng})[natural];
      way(around:${radiusMeters},${lat},${lng})[leisure];
      relation(around:${radiusMeters},${lat},${lng})[leisure];
      way(around:${radiusMeters},${lat},${lng})[amenity];
      relation(around:${radiusMeters},${lat},${lng})[amenity];
      way(around:${radiusMeters},${lat},${lng})[tourism];
      relation(around:${radiusMeters},${lat},${lng})[tourism];
      way(around:${radiusMeters},${lat},${lng})[place];
      relation(around:${radiusMeters},${lat},${lng})[place];
    );
    out geom;`;
    const url = 'https://overpass-api.de/api/interpreter';

    try {
        const startTime = Date.now();
        const response = await fetch(url, {
            method: 'POST',
            body: query,
            headers: { 'Content-Type': 'text/plain' }
        });

        const emptyResult = { type: 'FeatureCollection', features: [] };
        if (!response.ok) {
            console.log(`[OVERPASS-CACHE] API request failed with status: ${response.status}`);
            setCachedResult(cacheKey, emptyResult);
            return emptyResult;
        }

        const data = await response.json();
        const duration = Date.now() - startTime;
        console.log(`[OVERPASS-CACHE] API request completed in ${duration}ms`);
        console.log(`[OVERPASS-DEBUG] Received ${data.elements?.length || 0} elements from API`);

        if (data.elements && data.elements.length > 0) {
            const elementTypes = data.elements.reduce((acc, el) => {
                acc[el.type] = (acc[el.type] || 0) + 1;
                return acc;
            }, {});
            console.log(`[OVERPASS-DEBUG] Element types: ${Object.entries(elementTypes).map(([type, count]) => `${type}(${count})`).join(', ')}`);
        }

        if (!data.elements || data.elements.length === 0) {
            console.log(`[OVERPASS-DEBUG] No elements received - returning empty result`);
            setCachedResult(cacheKey, emptyResult);
            return emptyResult;
        }
        const features = [];

        // Process elements with 'out geom' format (includes geometry directly)
        for (const el of data.elements) {
            // Handle both ways and relations with geometry
            if ((el.type === 'way' || el.type === 'relation') && el.geometry && el.tags) {
                // Check all possible tag types from our Overpass query
                let typeTag = null;
                let valueTag = null;

                // Priority order: landuse > natural > leisure > amenity > tourism > place
                if (el.tags.landuse) {
                    typeTag = 'landuse';
                    valueTag = el.tags.landuse;
                } else if (el.tags.natural) {
                    typeTag = 'natural';
                    valueTag = el.tags.natural;
                } else if (el.tags.leisure) {
                    typeTag = 'leisure';
                    valueTag = el.tags.leisure;
                } else if (el.tags.amenity) {
                    typeTag = 'amenity';
                    valueTag = el.tags.amenity;
                } else if (el.tags.tourism) {
                    typeTag = 'tourism';
                    valueTag = el.tags.tourism;
                } else if (el.tags.place) {
                    typeTag = 'place';
                    valueTag = el.tags.place;
                }

                if (!typeTag || !valueTag) continue;

                // Process geometry from 'out geom' format
                let coords = null;

                if (el.type === 'way' && el.geometry && el.geometry.length >= 3) {
                    // Ways have simple geometry array
                    coords = el.geometry.map(node => [node.lon, node.lat]);
                } else if (el.type === 'relation' && el.geometry) {
                    // Relations might have more complex geometry structure
                    if (Array.isArray(el.geometry) && el.geometry.length >= 3) {
                        coords = el.geometry.map(node => [node.lon, node.lat]);
                    } else if (el.geometry.coordinates) {
                        // Some relations might have GeoJSON-like structure
                        coords = el.geometry.coordinates[0] || el.geometry.coordinates;
                    }
                }

                if (coords && coords.length >= 3) {
                    // Ensure polygon is closed
                    const first = coords[0];
                    const last = coords[coords.length - 1];
                    if (first[0] !== last[0] || first[1] !== last[1]) {
                        coords.push([first[0], first[1]]);
                    }

                    if (coords.length >= 4) {
                        features.push({
                            type: 'Feature',
                            geometry: {
                                type: 'Polygon',
                                coordinates: [coords]
                            },
                            properties: {
                                osm_id: el.id,
                                type: typeTag,
                                value: valueTag,
                                originalTag: typeTag,
                                originalValue: valueTag,
                                element_type: el.type // Track if it's way or relation
                            }
                        });
                    }
                }
            }
        }

        const result = { type: 'FeatureCollection', features };

        // Debug: Analyze loaded features
        const featureTypes = {};
        const tagTypes = {};
        features.forEach(f => {
            featureTypes[f.properties.value] = (featureTypes[f.properties.value] || 0) + 1;
            tagTypes[f.properties.originalTag] = (tagTypes[f.properties.originalTag] || 0) + 1;
        });

        console.log(`[OVERPASS-CACHE] ✅ Successfully loaded ${features.length} landuse features`);
        console.log(`[OVERPASS-CACHE] 📊 Feature types: ${Object.entries(featureTypes).map(([type, count]) => `${type}(${count})`).join(', ')}`);
        console.log(`[OVERPASS-CACHE] 🏷️ Tag types: ${Object.entries(tagTypes).map(([tag, count]) => `${tag}(${count})`).join(', ')}`);

        // Debug: Log forest-related features specifically
        const forestFeatures = features.filter(f =>
            f.properties.value === 'forest' ||
            f.properties.value === 'wood' ||
            f.properties.originalValue === 'forest' ||
            f.properties.originalValue === 'wood'
        );
        console.log(`[OVERPASS-DEBUG] 🌲 Forest/wood features found: ${forestFeatures.length}`);

        if (forestFeatures.length > 0) {
            forestFeatures.slice(0, 3).forEach((f, i) => {
                console.log(`[OVERPASS-DEBUG]   Forest ${i+1}: ${f.properties.originalTag}=${f.properties.originalValue} (${f.properties.element_type}, ID: ${f.properties.osm_id})`);
            });
        }

        // Ergebnis im Cache speichern
        setCachedResult(cacheKey, result);
        console.log(`[OVERPASS-CACHE] 💾 Cached result for: ${cacheKey}`);

        return result;
    } catch (e) {
        console.error(`[OVERPASS-CACHE] API request failed:`, e);
        const emptyResult = { type: 'FeatureCollection', features: [] };
        // Auch Fehler-Ergebnisse cachen (für kürzere Zeit)
        setCachedResult(cacheKey, emptyResult);
        return emptyResult;
    }
}

export async function getLanduseForLatLng(lat, lng) {
    // Cache-Schlüssel generieren (mit fester Radius von 50m)
    const cacheKey = generateCacheKey(lat, lng, 50);

    // Prüfen ob Ergebnis im Cache vorhanden ist
    const cachedResult = getCachedResult(cacheKey);
    if (cachedResult) {
        return cachedResult;
    }

    console.log(`[OVERPASS-CACHE] Cache miss for point query, making API request for: ${cacheKey}`);

    // Overpass QL: hole alle landuse-Polygone im Umkreis von 50m
    const query = `[out:json];(
      way["landuse"](around:50,${lat},${lng});
      way["natural"](around:50,${lat},${lng});
      way["leisure"](around:50,${lat},${lng});
    );
    (._;>;);
    out body;`;
    const url = 'https://overpass-api.de/api/interpreter';

    try {
        const startTime = Date.now();
        const response = await fetch(url, {
            method: 'POST',
            body: query,
            headers: { 'Content-Type': 'text/plain' }
        });

        const unknownResult = {type: null, value: 'unbekannt'};
        if (!response.ok) {
            console.log(`[OVERPASS-CACHE] Point query API request failed with status: ${response.status}`);
            setCachedResult(cacheKey, unknownResult);
            return unknownResult;
        }

        const data = await response.json();
        const duration = Date.now() - startTime;
        console.log(`[OVERPASS-CACHE] Point query API request completed in ${duration}ms`);
        ('[OVERPASS-LANDUSE] Antwort:', data);
        if (!data.elements || data.elements.length === 0) {
            ('[OVERPASS-LANDUSE] Keine Elemente erhalten!');
            setCachedResult(cacheKey, unknownResult);
            return unknownResult;
        }
        // Baue ein Knoten-Id -> Koordinate Mapping
        const nodes = {};
        for (const el of data.elements) {
            if (el.type === 'node') {
                nodes[el.id] = [el.lat, el.lon];
            }
        }
        let found = false;
        let polyCount = 0;
        // Prüfe für jedes Polygon, ob der Punkt enthalten ist
        for (const el of data.elements) {
            // Prüfe alle Polygone (landuse, natural, leisure)
            if (el.type === 'way' && el.tags && el.nodes && el.nodes.length >= 3) {
                // Bestimme den Typ des Polygons (landuse, natural, leisure)
                let typeTag = null;
                let valueTag = null;

                if (el.tags.landuse) {
                    typeTag = 'landuse';
                    valueTag = el.tags.landuse;
                } else if (el.tags.natural) {
                    typeTag = 'natural';
                    valueTag = el.tags.natural;
                } else if (el.tags.leisure) {
                    typeTag = 'leisure';
                    valueTag = el.tags.leisure;
                }

                // Wenn kein Typ gefunden wurde, überspringe dieses Polygon
                if (!typeTag || !valueTag) continue;

                const polygon = el.nodes.map(nid => nodes[nid]).filter(Boolean);
                polyCount++;
                console.log(`[OVERPASS-LANDUSE] Prüfe Polygon: ${typeTag}=${valueTag}`);

                if (polygon.length >= 3) {
                    // Standard-Test (lat,lon)
                    const inside = pointInPolygon(lat, lng, polygon);
                    // Alternativ: Test mit vertauschter Reihenfolge (lon,lat)
                    let insideSwapped = false;
                    if (!inside) {
                        const swappedPoly = polygon.map(([a,b]) => [b,a]);
                        insideSwapped = pointInPolygon(lng, lat, swappedPoly);
                    }

                    console.log(`[OVERPASS-LANDUSE] Punkt-in-Polygon: ${inside} (swapped: ${insideSwapped}) für Typ ${typeTag}=${valueTag}`);

                    if (inside || insideSwapped) {
                        found = true;
                        const result = {type: typeTag, value: valueTag};
                        setCachedResult(cacheKey, result);
                        console.log(`[OVERPASS-CACHE] Found landuse ${typeTag}=${valueTag} and cached result`);
                        return result;
                    }
                }
            }
        }
        (`[OVERPASS-LANDUSE] Anzahl geprüfter Polygone: ${polyCount}`);
        if (!found) {
            ('[OVERPASS-LANDUSE] Kein passendes Polygon gefunden!');
        }

        setCachedResult(cacheKey, unknownResult);
        console.log(`[OVERPASS-CACHE] No landuse found, cached unknown result`);
        return unknownResult;
    } catch (e) {
        console.error(`[OVERPASS-CACHE] Point query failed:`, e);
        setCachedResult(cacheKey, unknownResult);
        return unknownResult;
    }
}

// Cache-Management-Funktionen für Debugging und Wartung
export function getCacheStats() {
    const now = Date.now();
    let validEntries = 0;
    let expiredEntries = 0;

    for (const [key, entry] of overpassCache.entries()) {
        if ((now - entry.timestamp) < CACHE_DURATION_MS) {
            validEntries++;
        } else {
            expiredEntries++;
        }
    }

    return {
        totalEntries: overpassCache.size,
        validEntries,
        expiredEntries,
        maxEntries: MAX_CACHE_ENTRIES,
        cacheDurationMinutes: CACHE_DURATION_MS / (60 * 1000)
    };
}

export function clearCache() {
    const clearedCount = overpassCache.size;
    overpassCache.clear();
    console.log(`[OVERPASS-CACHE] Cleared ${clearedCount} cache entries`);
    return clearedCount;
}

export function cleanupExpiredCache() {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [key, entry] of overpassCache.entries()) {
        if ((now - entry.timestamp) >= CACHE_DURATION_MS) {
            overpassCache.delete(key);
            cleanedCount++;
        }
    }

    if (cleanedCount > 0) {
        console.log(`[OVERPASS-CACHE] Cleaned up ${cleanedCount} expired cache entries`);
    }

    return cleanedCount;
}

