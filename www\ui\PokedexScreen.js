// ui/PokedexScreen.js
// Pokedex screen component

import { Component } from './Component.js';
import { gameState } from '../state/game-state.js';
import { logger } from '../utils/logger.js';
import { getGermanPokemonName } from '../utils/pokemon-display-names.js';

export class PokedexScreen extends Component {
  constructor(container, options = {}) {
    super(container, options);
    this.pokedex = options.pokedex || gameState.pokedex;
    this.typeColors = this.getTypeColors();
    this.elements = {}; // Initialize elements object
    this.isRendered = false;
    this.pokedexDataLoadedListener = null; // Store listener for cleanup
  }

  /**
   * Get type colors from CSS variables
   * @returns {Object} - Map of type to color
   */
  getTypeColors() {
    // List of types as in type-colors.css
    const types = [
      'bug', 'dark', 'dragon', 'electric', 'fairy', 'fighting', 'fire', 'flying',
      'ghost', 'grass', 'ground', 'ice', 'normal', 'poison', 'psychic', 'rock',
      'steel', 'water'
    ];

    const style = getComputedStyle(document.documentElement);
    const typeColors = {};

    types.forEach(type => {
      typeColors[type] = style.getPropertyValue(`--type-${type}`).trim();
    });

    return typeColors;
  }

  /**
   * Render the Pokedex screen
   * @returns {HTMLElement} - The rendered container
   */
  async render() {
    try {
      // Get pokedex data from gameState (loaded via PokedexDataManager)
      let pokedexData = gameState.pokedexData || [];

      // If pokedex data is not loaded yet, wait for it or trigger loading
      if (!pokedexData || pokedexData.length === 0) {
        logger.debug('Pokedex data not available, waiting for gameState to load it');

        // Subscribe to pokedexDataLoaded event for re-render
        if (!this.pokedexDataLoadedListener) {
          this.pokedexDataLoadedListener = gameState.events.on('pokedexDataLoaded', () => {
            logger.debug('Pokedex data loaded event received, re-rendering');
            this.render(); // Re-render when data arrives
          });
        }

        // If gameState hasn't loaded data yet, trigger loading
        if (!gameState.pokedexData || gameState.pokedexData.length === 0) {
          await gameState.loadPokedexData();
          pokedexData = gameState.pokedexData || [];
        }
      }

      // Create maps for quick access
      const nameToDex = {};
      const idToDex = {};

      (pokedexData || []).forEach(p => {
        nameToDex[(p.name || '').toLowerCase()] = p.dex_number;
        idToDex[p.id] = p.dex_number;
      });

      // Get unique Pokemon by dex number
      const uniqueByDex = {};

      for (const p of this.pokedex) {
        let dex = p.dex_number;
        if (!dex) {
          // Try to get dex_number from name or id
          dex = nameToDex[(p.name || '').toLowerCase()] || idToDex[p.id];
        }
        if (dex && !uniqueByDex[dex]) {
          uniqueByDex[dex] = { ...p, dex_number: dex };
        }
      }

      // Sort by dex number
      const sorted = Object.values(uniqueByDex).sort((a, b) => (a.dex_number || 999) - (b.dex_number || 999));
      const total = 151;

      // Render the header and grid
      const gridContent = sorted.length > 0
        ? sorted.map(pokemon => this.renderPokemonCard(pokemon)).join('')
        : '<div class="pokedex-empty"><p>Noch keine Pokémon gefangen!</p><p>Gehe auf Entdeckungsreise und fange dein erstes Pokémon!</p></div>';

      this.container.innerHTML = `
        <div class="screen-header pokedex-header">
          <button class="back-btn" id="pokedex-back-btn" aria-label="Zurück">
            <img src="./icons/materialicons/chevronleft.svg" alt="Zurück" class="icon-svg" width="38" height="38" />
          </button>
          <h1>Pokédex</h1>
          <span class="pokedex-count header-right">${sorted.length} / ${total}</span>
        </div>
        <div class="pokedex-grid">
          ${gridContent}
        </div>
      `;

      // Store elements for event handling - always set this after innerHTML
      this.elements.backButton = this.container.querySelector('#pokedex-back-btn');

      this.isRendered = true;
      return this.container;
    } catch (e) {
      logger.error('Error rendering Pokedex screen:', e);
      this.container.innerHTML = `
        <div class="screen-header pokedex-header">
          <button class="back-btn" id="pokedex-back-btn" aria-label="Zurück">
            <img src="./icons/materialicons/chevronleft.svg" alt="Zurück" class="icon-svg" width="38" height="38" />
          </button>
          <h1>Pokédex</h1>
          <span class="pokedex-count header-right">Error</span>
        </div>
        <div class="pokedex-error">
          <p>Error loading Pokedex data. Please try again.</p>
        </div>
      `;

      // Store elements for event handling - always set this after innerHTML
      this.elements.backButton = this.container.querySelector('#pokedex-back-btn');

      this.isRendered = true;
      return this.container;
    }
  }

  /**
   * Render a Pokemon card
   * @param {Object} pokemon - The Pokemon data
   * @returns {string} - The HTML for the card
   */
  renderPokemonCard(pokemon) {
    const [type1, type2] = pokemon.types || [];
    let cardClass = type2 ? '' : `type-bg-${type1}`;
    let cardStyle = '';

    if (type2) {
      cardStyle = `background: linear-gradient(135deg, var(--type-${type1}) 60%, var(--type-${type2}) 80%);`;
    }

    // Get German name for display
    const displayName = getGermanPokemonName(pokemon);

    // Get image URL with fallback
    const imageUrl = pokemon.image_url || pokemon.imageUrl || (pokemon.dex_number ? `./src/PokemonSprites/${pokemon.dex_number}.png` : '');

    return `
      <div class="pokedex-card ${cardClass}"${cardStyle ? ` style="${cardStyle}"` : ''}>
        <div class="pokedex-card-dex">${String(pokemon.dex_number).padStart(3, '0')}</div>
        <div class="pokedex-card-name">${displayName}</div>
        <div class="pokedex-card-types">
          ${(pokemon.types || []).map(t => {
            const typeKey = t.toLowerCase();
            return `<span class="type-label type-${typeKey}">${t}</span>`;
          }).join('')}
        </div>
        <img class="pokedex-card-img" src="${imageUrl}" alt="${displayName}" />
      </div>
    `;
  }

  /**
   * Add event listeners
   */
  addEventListeners() {
    logger.debug('PokedexScreen: Adding event listeners');
    if (this.elements.backButton) {
      logger.debug('PokedexScreen: Back button found, adding click listener');
      this.addEventListener(this.elements.backButton, 'click', () => {
        logger.debug('PokedexScreen: Back button clicked');
        // Use standard overlay close method
        const overlay = this.container.closest('#pokedex-main-overlay');
        if (overlay) {
          overlay.style.display = 'none';
          overlay.dispatchEvent(new Event('closePokedex'));
        }
      });
    } else {
      logger.warn('PokedexScreen: Back button not found!');
    }
  }

  /**
   * Cleanup method to remove event listeners
   * This method is called when the overlay is closed
   */
  cleanup() {
    // Remove pokedexDataLoaded event listener
    if (this.pokedexDataLoadedListener) {
      this.pokedexDataLoadedListener(); // Call the unsubscribe function returned by events.on()
      this.pokedexDataLoadedListener = null;
      logger.debug('PokedexScreen: Removed pokedexDataLoaded event listener');
    }

    // Call parent destroy to clean up base class resources
    if (super.destroy) {
      super.destroy();
    }
  }
}

/**
 * Open the Pokedex screen
 */
export async function openPokedexScreen() {
  logger.debug('Opening Pokedex screen');

  // Import required modules
  const { OverlayManager } = await import('../utils/overlay-manager.js');

  const pokedexScreen = new PokedexScreen(null, { pokedex: gameState.pokedex });

  // Use OverlayManager for consistent behavior with cleanup callback
  const { overlay } = await OverlayManager.setupOverlay(
    'pokedex-main-overlay',
    'pokedex-main-overlay',
    'closePokedex',
    null, // No additional close callback
    () => pokedexScreen.cleanup() // Use cleanup callback for component cleanup
  );

  // Set the container after overlay is created
  pokedexScreen.container = overlay;
  await pokedexScreen.render();
  pokedexScreen.addEventListeners();

  logger.debug('Pokedex screen setup complete');
}
