<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Battle XP Validation Test</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            margin: 20px;
            background-color: #1a1a1a;
            color: #00ff00;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        h1 {
            color: #ffff00;
            text-align: center;
        }
        .test-output {
            background-color: #000;
            border: 1px solid #333;
            padding: 20px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-size: 14px;
            max-height: 600px;
            overflow-y: auto;
        }
        .run-button {
            background-color: #0066cc;
            color: white;
            border: none;
            padding: 10px 20px;
            font-size: 16px;
            border-radius: 5px;
            cursor: pointer;
            margin-bottom: 20px;
        }
        .run-button:hover {
            background-color: #0052a3;
        }
        .status {
            padding: 10px;
            margin-bottom: 20px;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success {
            background-color: #004d00;
            color: #00ff00;
        }
        .status.error {
            background-color: #4d0000;
            color: #ff0000;
        }
        .status.info {
            background-color: #003d4d;
            color: #00ccff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Battle XP Calculation Validation</h1>
        
        <div class="status info" id="status">
            Bereit zum Testen der XP-Berechnungskonsistenz zwischen BattleScreen und TrainerBattleScreen
        </div>
        
        <button class="run-button" onclick="runValidation()">Validierung starten</button>
        
        <div class="test-output" id="output">
            Klicke auf "Validierung starten" um die Tests auszuführen...
        </div>
    </div>

    <script type="module">
        import { validateXPCalculation } from './battle-xp-validation.js';
        
        // Override console methods to capture output
        const originalLog = console.log;
        const originalError = console.error;
        let testOutput = '';
        
        function captureConsole() {
            testOutput = '';
            console.log = function(...args) {
                testOutput += args.join(' ') + '\n';
                originalLog.apply(console, args);
            };
            console.error = function(...args) {
                testOutput += 'ERROR: ' + args.join(' ') + '\n';
                originalError.apply(console, args);
            };
        }
        
        function restoreConsole() {
            console.log = originalLog;
            console.error = originalError;
        }
        
        window.runValidation = async function() {
            const statusEl = document.getElementById('status');
            const outputEl = document.getElementById('output');
            
            statusEl.textContent = 'Tests werden ausgeführt...';
            statusEl.className = 'status info';
            outputEl.textContent = 'Tests laufen...\n';
            
            try {
                captureConsole();
                const result = validateXPCalculation();
                restoreConsole();
                
                outputEl.textContent = testOutput;
                
                if (result) {
                    statusEl.textContent = '✅ Alle Tests bestanden! XP-Berechnung ist konsistent.';
                    statusEl.className = 'status success';
                } else {
                    statusEl.textContent = '❌ Einige Tests fehlgeschlagen. Bitte Implementierung prüfen.';
                    statusEl.className = 'status error';
                }
            } catch (error) {
                restoreConsole();
                statusEl.textContent = `❌ Fehler beim Ausführen der Tests: ${error.message}`;
                statusEl.className = 'status error';
                outputEl.textContent = `Fehler: ${error.message}\n${error.stack}`;
            }
        };
    </script>
</body>
</html>
