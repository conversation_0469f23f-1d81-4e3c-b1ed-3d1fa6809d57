# Browser Testing Guide für GPS Pokemon App

## Übersicht

Die App wurde so erweitert, dass sie sowohl auf dem Smartphone (mit Capacitor) als auch im Browser (mit nativer Geolocation API) funktioniert.

## Implementierte Lösung

### Automatische Erkennung
- **Capacitor verfügbar**: Verwendet Capacitor Geolocation Plugin (für Android/iOS)
- **Browser**: Verwendet native `navigator.geolocation` API (für localhost)
- **Test-Modus**: Verwendet Mock-Daten (für test.html)

### Fallback-Strategie
Die `geolocation.js` Datei implementiert eine intelligente Fallback-Strategie:

1. **Prüfung auf Capacitor**: `window.Capacitor?.Plugins?.Geolocation && window.Capacitor?.isNativePlatform?.()`
2. **Fallback zu Browser**: `navigator.geolocation`
3. **Fehlerbehandlung**: <PERSON>n keine Methode verfügbar ist

## Browser-Testing Anleitung

### 1. HTTPS verwenden (Empfohlen)
Moderne Browser erfordern HTTPS für Geolocation:

```powershell
# Mit Ionic CLI (falls installiert) im Hauptverzeichnis (Ionic Projekt wurde angelegt)
ionic serve --ssl

# Oder mit einem lokalen HTTPS-Server
# Installieren Sie http-server mit SSL-Support
npm install -g http-server
http-server www -S -C cert.pem -K key.pem -p 8080
```

### 2. HTTP localhost (Alternative)
Falls HTTPS nicht verfügbar ist, funktioniert `http://localhost` in den meisten Browsern:

```powershell
# Einfacher HTTP-Server
npx http-server www -p 8080
```

Dann öffnen Sie: `http://localhost:8080`

### 3. Browser-Berechtigungen
- Der Browser wird automatisch nach Standort-Berechtigung fragen
- Klicken Sie auf "Zulassen" wenn der Dialog erscheint
- Bei Chrome: Klicken Sie auf das Schloss-Symbol in der Adressleiste um Berechtigungen zu verwalten

### 4. Debugging
Öffnen Sie die Browser-Entwicklertools (F12) um Logs zu sehen:
- **Capacitor**: "Using Capacitor Geolocation..."
- **Browser**: "Using Browser Geolocation API..."
- **Test-Modus**: "Using test mode..."

## Funktionen

### Unterstützte Geolocation-Features
- ✅ `startWatchPosition()` - Kontinuierliche Positionsüberwachung
- ✅ `getCurrentPosition()` - Einmalige Positionsabfrage
- ✅ `checkLocationPermissions()` - Berechtigungsprüfung
- ✅ `requestLocationPermissions()` - Berechtigungsanfrage

### Konfiguration
Die Geolocation-Einstellungen werden aus `config.js` gelesen:
```javascript
geolocation: {
  enableHighAccuracy: true,
  timeout: 10000,
  maximumAge: 0
}
```

## Troubleshooting

### Problem: "Geolocation not supported"
- **Lösung**: Verwenden Sie einen modernen Browser (Chrome, Firefox, Safari, Edge)

### Problem: "Permission denied"
- **Lösung**: Erlauben Sie Standort-Zugriff im Browser
- **Chrome**: Einstellungen > Datenschutz und Sicherheit > Website-Einstellungen > Standort

### Problem: "Position unavailable"
- **Lösung**: Stellen Sie sicher, dass GPS/WLAN-Ortung aktiviert ist
- **Alternative**: Verwenden Sie den Test-Modus mit Mock-Daten

### Problem: HTTPS-Zertifikat Fehler
- **Lösung**: Akzeptieren Sie das selbst-signierte Zertifikat im Browser
- **Alternative**: Verwenden Sie `http://localhost` (funktioniert in den meisten Browsern)

## Entwicklung

### Code-Struktur
```
www/capacitor/geolocation.js
├── isCapacitorAvailable()     # Prüft Capacitor-Verfügbarkeit
├── isBrowserGeolocationAvailable()  # Prüft Browser-Geolocation
├── startWatchPosition()       # Hauptfunktion mit Fallback-Logik
├── startCapacitorWatchPosition()    # Capacitor-spezifisch
├── startBrowserWatchPosition()      # Browser-spezifisch
└── getCurrentPosition()       # Einmalige Position mit Fallback
```

### Logging
Alle Geolocation-Aktivitäten werden geloggt:
- `logger.info()` für wichtige Ereignisse
- `logger.debug()` für Positionsupdates
- `logger.error()` für Fehler

## Testen

### Smartphone (Capacitor)
```powershell
# Android Studio Build
ionic capacitor run android
```

### Browser (localhost)
```powershell
# HTTPS (empfohlen)
ionic serve --ssl

# HTTP (Alternative)
npx http-server www -p 8080
```

### Test-Modus (Mock-Daten)
Öffnen Sie `test.html` für Mock-GPS-Daten (Berlin-Koordinaten).
