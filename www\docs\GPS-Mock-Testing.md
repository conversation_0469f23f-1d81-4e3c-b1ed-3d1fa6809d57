# GPS Mock Testing System

Das GPS Mock System ermöglicht es, GPS-Positionen im Browser zu simulieren, um die App zu testen, ohne sich physisch bewegen zu müssen.

## Aktivierung

### 1. GPS Mock aktivieren
```javascript
testGPS.enableMock(51.96, 7.62)  // Startposition setzen
```

### 2. Mausklick-Modus aktivieren (nur Browser)
```javascript
testGPS.enableMapClick()
```

Nach der Aktivierung:
- Ein orangener Indikator erscheint oben rechts: "🧪 GPS Mock aktiv | 🖱️ Klick-Modus"
- **Echte GPS-Abfrage wird automatisch pausiert**
- Klicke auf die Karte, um dich zu der Position zu "bewegen"
- Pokemon-Spawns und alle anderen GPS-Events werden ausgelöst

## Verfügbare Befehle

### Grundlegende Funktionen
- `testGPS.enableMock(lat, lng)` - GPS Mock aktivieren
- `testGPS.disable()` - GPS Mock komplett deaktivieren
- `testGPS.moveTo(lat, lng)` - Direkt zu Position bewegen

### Mausklick-Steuerung
- `testGPS.enableMapClick()` - Mausklick-Modus aktivieren
- `testGPS.disableMapClick()` - Nur Mausklick-Modus deaktivieren

### Automatische Tests
- `testGPS.simulateWalk()` - Simuliert einen Spaziergang mit mehreren Punkten
- `testGPS.help()` - Zeigt alle verfügbaren Befehle

## Funktionsweise

Das System:
1. **Pausiert echte GPS-Abfrage** automatisch beim Aktivieren
2. Löst die gleichen Events aus wie echtes GPS
3. Triggert Pokemon-Spawns basierend auf Bewegungsdistanz
4. **Reaktiviert echte GPS-Abfrage** automatisch beim Deaktivieren
5. Funktioniert nur im Browser, nicht in der App

### GPS-Pause-System
- **Mock-Modus**: Pausiert echte GPS-Abfrage komplett
- **Walk-Modus**: Pausiert echte GPS-Abfrage während der Simulation
- **Nahtloser Übergang**: Echtes GPS wird automatisch reaktiviert
- **Keine Konflikte**: Mock und echtes GPS laufen nie gleichzeitig

## Visuelles Feedback

- **Orangener Indikator**: Zeigt an, dass GPS Mock aktiv ist
- **Klick-Modus Text**: Zeigt an, wenn Mausklicks aktiviert sind (🖱️ Klick-Modus)
- **Walk-Modus Text**: Zeigt an, wenn Walk-Simulation läuft (🚶 Walk-Modus)
- **Console-Logs**: Bestätigen jede Bewegung mit Koordinaten
- **GPS-Status**: Console-Meldungen über GPS-Pause/Reaktivierung

## Deaktivierung

```javascript
testGPS.disable()  // Deaktiviert alles, entfernt Indikator und reaktiviert echtes GPS
```

**Wichtig**: Nach `testGPS.disable()` wird die echte GPS-Abfrage automatisch reaktiviert und übernimmt nahtlos die Positionsbestimmung.

## Beispiel-Workflow

```javascript
// 1. GPS Mock starten
testGPS.enableMock(51.96, 7.62)

// 2. Mausklick-Modus aktivieren
testGPS.enableMapClick()

// 3. Auf die Karte klicken zum Bewegen
// Pokemon spawnen automatisch bei ausreichender Distanz

// 4. Deaktivieren wenn fertig
testGPS.disable()
```

## Hinweise

- Funktioniert nur im Browser, nicht in der Capacitor-App
- GPS Mock muss vor dem Mausklick-Modus aktiviert werden
- Alle normalen Spiel-Mechaniken (Pokemon-Spawns, Trainer-Battles, etc.) funktionieren normal
- Der Mock überschreibt echte GPS-Signale komplett
