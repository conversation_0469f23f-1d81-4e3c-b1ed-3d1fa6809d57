// storage/storage-service.js
// Unified storage service that works with both Capacitor Storage and localStorage

import { logger } from '../utils/logger.js';

export class StorageService {
  constructor() {
    this.capacitorAvailable = !!(window.Capacitor?.Plugins?.Storage);
    logger.debug('StorageService initialized', { capacitorAvailable: this.capacitorAvailable });
  }
  
  /**
   * Get a value from storage
   * @param {string} key - The key to get
   * @param {any} defaultValue - The default value if the key doesn't exist
   * @returns {Promise<any>} - The value from storage
   */
  async get(key, defaultValue = null) {
    try {
      if (this.capacitorAvailable) {
        const result = await window.Capacitor.Plugins.Storage.get({ key });
        return result.value ? JSON.parse(result.value) : defaultValue;
      } else {
        const value = localStorage.getItem(key);
        return value ? JSON.parse(value) : defaultValue;
      }
    } catch (e) {
      logger.error(`<PERSON>rror getting ${key} from storage:`, e);
      return defaultValue;
    }
  }
  
  /**
   * Helper method to write JSON value to storage
   * @param {string} key - The key to set
   * @param {string} jsonValue - The JSON stringified value
   * @returns {Promise<boolean>} - Whether the operation was successful
   */
  async _writeStorage(key, jsonValue) {
    try {
      if (this.capacitorAvailable) {
        await window.Capacitor.Plugins.Storage.set({ key, value: jsonValue });
      } else {
        localStorage.setItem(key, jsonValue);
      }
      logger.info(`Successfully saved ${key} to storage`);
      return true;
    } catch (e) {
      logger.error(`Error writing ${key} to storage:`, e);
      return false;
    }
  }

  /**
   * Set a value in storage
   * @param {string} key - The key to set
   * @param {any} value - The value to set
   * @returns {Promise<boolean>} - Whether the operation was successful
   */
  async set(key, value) {
    try {
      const jsonValue = JSON.stringify(value);
      const success = await this._writeStorage(key, jsonValue);
      if (success) {
        logger.debug(`Saved to storage: ${key}`);
        return true;
      }
      return false;
    } catch (e) {
      logger.error(`Error setting ${key} in storage:`, e);

      // If quota exceeded, try to clean up old data
      if (e.name === 'QuotaExceededError' || e.message.includes('quota')) {
        logger.warn('Storage quota exceeded, attempting cleanup...');

        // For timeEventSpawns, try aggressive cleanup first
        if (key.includes('timeEventSpawns')) {
          await this.cleanupTimeEventSpawns();
        }

        await this.cleanupOldData();

        // Try again after cleanup
        try {
          const jsonValue = JSON.stringify(value);
          const retrySuccess = await this._writeStorage(key, jsonValue);
          if (retrySuccess) {
            logger.info(`Successfully saved ${key} after cleanup`);
            return true;
          } else {
            logger.error(`Failed to save ${key} even after cleanup`);

            // Last resort: if it's timeEventSpawns, try to save only essential data
            if (key.includes('timeEventSpawns')) {
              logger.warn('Attempting to save reduced timeEventSpawns data...');
              const reducedValue = this.reduceTimeEventSpawnsData(value);
              const reducedJsonValue = JSON.stringify(reducedValue);
              const finalAttempt = await this._writeStorage(key, reducedJsonValue);
              if (finalAttempt) {
                logger.info(`Successfully saved reduced ${key} data`);
                return true;
              }
            }

            return false;
          }
        } catch (retryError) {
          logger.error(`Failed to save ${key} even after cleanup:`, retryError);
          return false;
        }
      }

      return false;
    }
  }
  
  /**
   * Remove a value from storage
   * @param {string} key - The key to remove
   * @returns {Promise<boolean>} - Whether the operation was successful
   */
  async remove(key) {
    try {
      if (this.capacitorAvailable) {
        await window.Capacitor.Plugins.Storage.remove({ key });
      } else {
        localStorage.removeItem(key);
      }
      logger.debug(`Removed from storage: ${key}`);
      return true;
    } catch (e) {
      logger.error(`Error removing ${key} from storage:`, e);
      return false;
    }
  }
  
  /**
   * Clear all values from storage
   * @returns {Promise<boolean>} - Whether the operation was successful
   */
  async clear() {
    try {
      if (this.capacitorAvailable) {
        await window.Capacitor.Plugins.Storage.clear();
      } else {
        localStorage.clear();
      }
      logger.debug('Storage cleared');
      return true;
    } catch (e) {
      logger.error('Error clearing storage:', e);
      return false;
    }
  }
  
  /**
   * Get all keys from storage
   * @returns {Promise<string[]>} - The keys in storage
   */
  async keys() {
    try {
      if (this.capacitorAvailable) {
        const { keys } = await window.Capacitor.Plugins.Storage.keys();
        return keys;
      } else {
        return Object.keys(localStorage);
      }
    } catch (e) {
      logger.error('Error getting keys from storage:', e);
      return [];
    }
  }

  /**
   * Clean up old data to free storage space
   * @returns {Promise<void>}
   */
  async cleanupOldData() {
    try {
      logger.info('Starting storage cleanup...');

      if (this.capacitorAvailable) {
        // Capacitor cleanup
        const { keys } = await window.Capacitor.Plugins.Storage.keys();
        const keysToRemove = [];

        // Remove old cache entries (older than 24 hours)
        const oneDayAgo = Date.now() - (24 * 60 * 60 * 1000);
        // For timeEventSpawns, be more aggressive (older than 2 hours)
        const twoHoursAgo = Date.now() - (2 * 60 * 60 * 1000);

        for (const key of keys) {
          if (key.startsWith('osm_ways_') || key.startsWith('landuse_cache_')) {
            try {
              const { value } = await window.Capacitor.Plugins.Storage.get({ key });
              if (value) {
                const data = JSON.parse(value);
                if (data.timestamp && data.timestamp < oneDayAgo) {
                  keysToRemove.push(key);
                }
              }
            } catch (e) {
              // If we can't parse it, remove it
              keysToRemove.push(key);
            }
          }
          // Aggressive cleanup for timeEventSpawns
          else if (key.startsWith('timeEventSpawns_')) {
            try {
              const { value } = await window.Capacitor.Plugins.Storage.get({ key });
              if (value) {
                const data = JSON.parse(value);
                if (data.timestamp && data.timestamp < twoHoursAgo) {
                  keysToRemove.push(key);
                }
              }
            } catch (e) {
              // If we can't parse it, remove it
              keysToRemove.push(key);
            }
          }
          // Remove any corrupted or very old entries
          else if (key.includes('_old_') || key.includes('_backup_')) {
            keysToRemove.push(key);
          }
        }

        // Remove old keys
        for (const key of keysToRemove) {
          await window.Capacitor.Plugins.Storage.remove({ key });
        }

        logger.info(`Cleaned up ${keysToRemove.length} old cache entries`);

      } else {
        // localStorage cleanup
        const keysToRemove = [];
        const oneDayAgo = Date.now() - (24 * 60 * 60 * 1000);
        const twoHoursAgo = Date.now() - (2 * 60 * 60 * 1000);

        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key && (key.startsWith('osm_ways_') || key.startsWith('landuse_cache_'))) {
            try {
              const value = localStorage.getItem(key);
              if (value) {
                const data = JSON.parse(value);
                if (data.timestamp && data.timestamp < oneDayAgo) {
                  keysToRemove.push(key);
                }
              }
            } catch (e) {
              // If we can't parse it, remove it
              keysToRemove.push(key);
            }
          }
          // Aggressive cleanup for timeEventSpawns
          else if (key && key.startsWith('timeEventSpawns_')) {
            try {
              const value = localStorage.getItem(key);
              if (value) {
                const data = JSON.parse(value);
                if (data.timestamp && data.timestamp < twoHoursAgo) {
                  keysToRemove.push(key);
                }
              }
            } catch (e) {
              // If we can't parse it, remove it
              keysToRemove.push(key);
            }
          }
          // Remove any corrupted or very old entries
          else if (key && (key.includes('_old_') || key.includes('_backup_'))) {
            keysToRemove.push(key);
          }
        }

        // Remove old keys
        keysToRemove.forEach(key => localStorage.removeItem(key));

        logger.info(`Cleaned up ${keysToRemove.length} old localStorage entries`);
      }

    } catch (error) {
      logger.error('Error during storage cleanup:', error);
    }
  }

  /**
   * Aggressive cleanup specifically for timeEventSpawns
   * @returns {Promise<void>}
   */
  async cleanupTimeEventSpawns() {
    try {
      logger.info('Starting aggressive timeEventSpawns cleanup...');

      if (this.capacitorAvailable) {
        const { keys } = await window.Capacitor.Plugins.Storage.keys();
        const timeEventKeys = keys.filter(key => key.includes('timeEventSpawns'));

        // Remove all but the most recent timeEventSpawns entry
        if (timeEventKeys.length > 1) {
          // Sort by timestamp (assuming keys contain timestamps)
          timeEventKeys.sort();
          const keysToRemove = timeEventKeys.slice(0, -1); // Keep only the last one

          for (const key of keysToRemove) {
            await window.Capacitor.Plugins.Storage.remove({ key });
          }

          logger.info(`Removed ${keysToRemove.length} old timeEventSpawns entries`);
        }
      } else {
        // localStorage cleanup
        const timeEventKeys = [];
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key && key.includes('timeEventSpawns')) {
            timeEventKeys.push(key);
          }
        }

        if (timeEventKeys.length > 1) {
          timeEventKeys.sort();
          const keysToRemove = timeEventKeys.slice(0, -1);

          keysToRemove.forEach(key => localStorage.removeItem(key));
          logger.info(`Removed ${keysToRemove.length} old timeEventSpawns entries from localStorage`);
        }
      }
    } catch (error) {
      logger.error('Error during timeEventSpawns cleanup:', error);
    }
  }

  /**
   * Reduce timeEventSpawns data to essential information only
   * @param {Object} data - The original timeEventSpawns data
   * @returns {Object} - Reduced data
   */
  reduceTimeEventSpawnsData(data) {
    try {
      if (!data || typeof data !== 'object') return data;

      // Keep only essential fields, remove verbose data
      const reduced = {
        timestamp: data.timestamp,
        spawns: []
      };

      if (data.spawns && Array.isArray(data.spawns)) {
        // Keep only essential spawn data
        reduced.spawns = data.spawns.map(spawn => ({
          id: spawn.id,
          lat: spawn.lat,
          lng: spawn.lng,
          pokemonId: spawn.pokemonId,
          level: spawn.level,
          // Remove large fields like detailed stats, descriptions, etc.
        })).slice(0, 50); // Limit to 50 most recent spawns
      }

      logger.info(`Reduced timeEventSpawns data from ${JSON.stringify(data).length} to ${JSON.stringify(reduced).length} characters`);
      return reduced;
    } catch (error) {
      logger.error('Error reducing timeEventSpawns data:', error);
      return data; // Return original if reduction fails
    }
  }

  /**
   * Get storage usage information
   * @returns {Promise<Object>} Storage usage stats
   */
  async getStorageInfo() {
    try {
      if (this.capacitorAvailable) {
        const { keys } = await window.Capacitor.Plugins.Storage.keys();
        return {
          totalKeys: keys.length,
          cacheKeys: keys.filter(k => k.startsWith('osm_ways_') || k.startsWith('landuse_cache_')).length,
          gameDataKeys: keys.filter(k => k === 'timeEventSpawns' || k === 'gameState').length
        };
      } else {
        const totalKeys = localStorage.length;
        const cacheKeys = [];
        const gameDataKeys = [];

        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key) {
            if (key.startsWith('osm_ways_') || key.startsWith('landuse_cache_')) {
              cacheKeys.push(key);
            } else if (key === 'timeEventSpawns' || key === 'gameState') {
              gameDataKeys.push(key);
            }
          }
        }

        return {
          totalKeys,
          cacheKeys: cacheKeys.length,
          gameDataKeys: gameDataKeys.length
        };
      }
    } catch (error) {
      logger.error('Error getting storage info:', error);
      return { totalKeys: 0, cacheKeys: 0, gameDataKeys: 0 };
    }
  }
}

// Export a singleton instance
export const storageService = new StorageService();
