// battle-xp-validation.js
// Validation test for XP calculation consistency between BattleScreen and TrainerBattleScreen

import { calculateExpProgress } from '../utils/battle-utils.js';
import { getExpForLevel, getExpCurveForRarity } from '../services/experience-system.js';

/**
 * Test Pokemon data for validation
 */
const testPokemon = [
  {
    name: 'Pika<PERSON>',
    level: 5,
    experience: 125,
    rarity: 'starter'
  },
  {
    name: '<PERSON><PERSON><PERSON>',
    level: 10,
    experience: 1000,
    rarity: 'starter'
  },
  {
    name: 'Pidgey',
    level: 15,
    experience: 3375,
    rarity: 'common'
  },
  {
    name: 'Dragonite',
    level: 20,
    experience: 8000,
    rarity: 'rare'
  },
  {
    name: 'Mew',
    level: 25,
    experience: 15625,
    rarity: 'mythical'
  }
];

/**
 * Validate XP calculation consistency
 */
function validateXPCalculation() {
  console.log('=== Battle XP Calculation Validation ===\n');
  
  let allTestsPassed = true;
  
  testPokemon.forEach((pokemon, index) => {
    console.log(`Test ${index + 1}: ${pokemon.name} (Level ${pokemon.level}, ${pokemon.rarity})`);
    console.log(`Current XP: ${pokemon.experience}`);
    
    try {
      // Test basic calculation
      const expProgress = calculateExpProgress(pokemon);
      
      console.log(`Current Level XP: ${expProgress.currentLevelExp}`);
      console.log(`Next Level XP: ${expProgress.nextLevelExp}`);
      console.log(`XP in Current Level: ${expProgress.expInCurrentLevel}`);
      console.log(`XP Needed for Next Level: ${expProgress.expNeededForNextLevel}`);
      console.log(`Progress Percentage: ${expProgress.progressPercentage}%`);
      
      // Test with additional XP
      const additionalXP = 100;
      const expProgressWithBonus = calculateExpProgress(pokemon, additionalXP);
      
      console.log(`With +${additionalXP} XP:`);
      console.log(`New Progress Percentage: ${expProgressWithBonus.newExpPercentage}%`);
      console.log(`Will Level Up: ${expProgressWithBonus.willLevelUp}`);
      
      // Validate consistency
      const expectedCurrentLevelXP = getExpForLevel(pokemon.level, getExpCurveForRarity(pokemon.rarity));
      const expectedNextLevelXP = getExpForLevel(pokemon.level + 1, getExpCurveForRarity(pokemon.rarity));
      
      if (expProgress.currentLevelExp !== expectedCurrentLevelXP) {
        console.error(`❌ Current Level XP mismatch: expected ${expectedCurrentLevelXP}, got ${expProgress.currentLevelExp}`);
        allTestsPassed = false;
      }
      
      if (expProgress.nextLevelExp !== expectedNextLevelXP) {
        console.error(`❌ Next Level XP mismatch: expected ${expectedNextLevelXP}, got ${expProgress.nextLevelExp}`);
        allTestsPassed = false;
      }
      
      // Validate percentage calculation
      const expectedExpInLevel = pokemon.experience - expectedCurrentLevelXP;
      const expectedExpNeeded = expectedNextLevelXP - expectedCurrentLevelXP;
      const expectedPercentage = Math.min(100, Math.floor((expectedExpInLevel / expectedExpNeeded) * 100));
      
      if (expProgress.progressPercentage !== expectedPercentage) {
        console.error(`❌ Progress percentage mismatch: expected ${expectedPercentage}%, got ${expProgress.progressPercentage}%`);
        allTestsPassed = false;
      }
      
      console.log('✅ Test passed\n');
      
    } catch (error) {
      console.error(`❌ Test failed with error: ${error.message}`);
      allTestsPassed = false;
    }
  });
  
  // Test edge cases
  console.log('=== Edge Case Tests ===\n');
  
  // Test with null Pokemon
  try {
    const nullResult = calculateExpProgress(null);
    if (nullResult.progressPercentage === 0 && nullResult.currentExp === 0) {
      console.log('✅ Null Pokemon test passed');
    } else {
      console.error('❌ Null Pokemon test failed');
      allTestsPassed = false;
    }
  } catch (error) {
    console.error(`❌ Null Pokemon test failed with error: ${error.message}`);
    allTestsPassed = false;
  }
  
  // Test with Pokemon at exact level boundary
  const boundaryPokemon = {
    name: 'Boundary Test',
    level: 10,
    experience: getExpForLevel(10, getExpCurveForRarity('common')),
    rarity: 'common'
  };
  
  try {
    const boundaryResult = calculateExpProgress(boundaryPokemon);
    if (boundaryResult.progressPercentage === 0) {
      console.log('✅ Level boundary test passed');
    } else {
      console.error(`❌ Level boundary test failed: expected 0%, got ${boundaryResult.progressPercentage}%`);
      allTestsPassed = false;
    }
  } catch (error) {
    console.error(`❌ Level boundary test failed with error: ${error.message}`);
    allTestsPassed = false;
  }
  
  // Test level up detection
  const levelUpPokemon = {
    name: 'Level Up Test',
    level: 5,
    experience: getExpForLevel(5, getExpCurveForRarity('common')) + 50,
    rarity: 'common'
  };
  
  const levelUpXP = getExpForLevel(6, getExpCurveForRarity('common')) - levelUpPokemon.experience + 10;
  
  try {
    const levelUpResult = calculateExpProgress(levelUpPokemon, levelUpXP);
    if (levelUpResult.willLevelUp) {
      console.log('✅ Level up detection test passed');
    } else {
      console.error('❌ Level up detection test failed');
      allTestsPassed = false;
    }
  } catch (error) {
    console.error(`❌ Level up detection test failed with error: ${error.message}`);
    allTestsPassed = false;
  }
  
  console.log('\n=== Validation Summary ===');
  if (allTestsPassed) {
    console.log('🎉 All tests passed! XP calculation is consistent.');
  } else {
    console.log('❌ Some tests failed. Please check the implementation.');
  }
  
  return allTestsPassed;
}

// Export for use in other test files
export { validateXPCalculation, testPokemon };

// Run validation if this file is executed directly
if (typeof window !== 'undefined' && window.location) {
  // Browser environment - can be run directly
  document.addEventListener('DOMContentLoaded', () => {
    validateXPCalculation();
  });
}
