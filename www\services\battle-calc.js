// services/battle-calc.js
// Service for calculating battle outcomes between Pokemon

import { logger } from '../utils/logger.js';
import { getExpFromBattle } from './experience-system.js';
import { getGermanPokemonName } from '../utils/pokemon-display-names.js';

// Rarity multipliers für die XP-Berechnung (Kopie aus experience-system.js für Debug-Zwecke)
const RARITY_MULTIPLIERS = {
  mythical:   3.0,
  legendary:  2.5,
  starter:    1.8,
  rare:       1.5,
  scarce:     1.2,
  common:     1.0
};
// Import JSON data using fetch instead of static import with assert
let typeEffectivenessData = {}; // Initialize as empty object

/**
 * Log battle information to console in a way that works in both browser and Capacitor
 * @param {string} message - The message to log
 */
function logToBattleConsole(message) {
  // Check if we're in a Capacitor environment
  if (window.Capacitor && window.Capacitor.isNativePlatform && window.Capacitor.isNativePlatform()) {
    // In Capacitor, use console.warn for better visibility in logcat
    console.warn('POKEMON_BATTLE_LOG: ' + message);
  } else {
    // In browser, use logger.debug
    logger.debug(message);
  }
}

// Fetch the type effectiveness data
async function loadTypeEffectivenessData() {
  try {
    const response = await fetch('./pokemon-types-battle.json');
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    typeEffectivenessData = data;
    logger.debug('Type effectiveness data loaded successfully');
    return data;
  } catch (error) {
    logger.error('Error loading type effectiveness data:', error);
    return null;
  }
}

// Load the data immediately
loadTypeEffectivenessData();

/**
 * Check if type effectiveness data is loaded
 * @returns {boolean} - Whether the data is loaded
 */
function isTypeDataLoaded() {
  return Object.keys(typeEffectivenessData).length > 0;
}

/**
 * Get type effectiveness data, using hardcoded data as fallback
 * @param {string} type - The type to get data for
 * @returns {Object} - The type effectiveness data
 */
function getTypeEffectivenessData(type) {
  // Normalize type to capitalize first letter (e.g. "electric" -> "Electric")
  const normalizedType = type.charAt(0).toUpperCase() + type.slice(1).toLowerCase();

  // If the type exists in the loaded data, use it
  if (typeEffectivenessData[normalizedType]) {
    return typeEffectivenessData[normalizedType];
  }

  // If the type exists in the hardcoded data, use it as fallback
  if (hardcodedTypeData[normalizedType]) {
    logger.debug(`Using hardcoded data for type: ${normalizedType}`);
    return hardcodedTypeData[normalizedType];
  }

  // Log the available types for debugging
  logger.warn(`No type effectiveness data found for type: ${type} (normalized: ${normalizedType})`);
  logger.debug(`Available types in loaded data: ${Object.keys(typeEffectivenessData).join(', ')}`);
  logger.debug(`Available types in hardcoded data: ${Object.keys(hardcodedTypeData).join(', ')}`);

  // If the type doesn't exist in either, return a default object
  return {
    super_effective: [],
    not_very_effective: [],
    no_effect: []
  };
}

/**
 * Calculate type effectiveness between attacker and defender types
 * @param {string[]} attackerTypes - The attacking Pokemon's types
 * @param {string[]} defenderTypes - The defending Pokemon's types
 * @returns {Object} - The effectiveness calculation result
 */
export function calculateTypeEffectiveness(attackerTypes, defenderTypes) {
  // Ensure types are arrays
  const attTypes = Array.isArray(attackerTypes) ? attackerTypes : ['Normal'];
  const defTypes = Array.isArray(defenderTypes) ? defenderTypes : ['Normal'];

  // Initialize result object
  const result = {
    totalMultiplier: 1.0,
    attackerToDefender: [],
    defenderToAttacker: []
  };

  // If type data is not loaded yet, return default values
  if (!isTypeDataLoaded()) {
    logger.warn('Type effectiveness data not loaded yet, using default values');

    // Create default entries for each attacker type
    attTypes.forEach(attackerType => {
      const typeResult = {
        attackerType,
        defenderTypes: defTypes.map(defenderType => ({
          defenderType,
          effectiveness: 'neutral',
          multiplier: 1.0
        })),
        multiplier: 1.0
      };
      result.attackerToDefender.push(typeResult);
    });

    // Create default entries for each defender type
    defTypes.forEach(defenderType => {
      const typeResult = {
        attackerType: defenderType,
        defenderTypes: attTypes.map(attackerType => ({
          defenderType: attackerType,
          effectiveness: 'neutral',
          multiplier: 1.0
        })),
        multiplier: 1.0
      };
      result.defenderToAttacker.push(typeResult);
    });

    return result;
  }

  // Calculate attacker's effectiveness against defender
  attTypes.forEach(attackerType => {
    // Get type effectiveness data with fallback
    const typeData = getTypeEffectivenessData(attackerType);

    let typeMultiplier = 1.0;
    const typeResult = {
      attackerType,
      defenderTypes: [],
      multiplier: 1.0
    };

    defTypes.forEach(defenderType => {
      let effectiveness = 'neutral';
      let multiplier = 1.0;

      // Normalize defender type for comparison with type effectiveness data
      const normalizedDefenderType = defenderType.charAt(0).toUpperCase() + defenderType.slice(1).toLowerCase();

      if (typeData.super_effective.includes(normalizedDefenderType)) {
        effectiveness = 'super';
        multiplier = 1.25; // Super effective
      } else if (typeData.not_very_effective.includes(normalizedDefenderType)) {
        effectiveness = 'not_very';
        multiplier = 0.8; // Not very effective
      } else if (typeData.no_effect.includes(normalizedDefenderType)) {
        effectiveness = 'no_effect';
        multiplier = 0.2; // No effect
      }

      typeResult.defenderTypes.push({
        defenderType,
        effectiveness,
        multiplier
      });

      typeMultiplier *= multiplier;
    });

    typeResult.multiplier = typeMultiplier;
    result.attackerToDefender.push(typeResult);
    result.totalMultiplier *= typeMultiplier;
  });

  // Calculate defender's effectiveness against attacker (reverse calculation)
  defTypes.forEach(defenderType => {
    // Get type effectiveness data with fallback
    const typeData = getTypeEffectivenessData(defenderType);

    let typeMultiplier = 1.0;
    const typeResult = {
      attackerType: defenderType,
      defenderTypes: [],
      multiplier: 1.0
    };

    attTypes.forEach(attackerType => {
      let effectiveness = 'neutral';
      let multiplier = 1.0;

      // Normalize attacker type for comparison with type effectiveness data
      const normalizedAttackerType = attackerType.charAt(0).toUpperCase() + attackerType.slice(1).toLowerCase();

      if (typeData.super_effective.includes(normalizedAttackerType)) {
        effectiveness = 'super';
        multiplier = 1.25; // Super effective
      } else if (typeData.not_very_effective.includes(normalizedAttackerType)) {
        effectiveness = 'not_very';
        multiplier = 0.8; // Not very effective
      } else if (typeData.no_effect.includes(normalizedAttackerType)) {
        effectiveness = 'no_effect';
        multiplier = 0.2; // No effect
      }

      typeResult.defenderTypes.push({
        defenderType: attackerType,
        effectiveness,
        multiplier
      });

      typeMultiplier *= multiplier;
    });

    typeResult.multiplier = typeMultiplier;
    result.defenderToAttacker.push(typeResult);
  });

  return result;
}

/**
 * Calculate the outcome of a battle between two Pokemon
 * @param {Object} playerPokemon - The player's Pokemon
 * @param {Object} wildPokemon - The wild Pokemon
 * @returns {Object} - The battle result
 */
export function calculateBattleOutcome(playerPokemon, wildPokemon) {
  try {
    logger.debug(`Calculating battle outcome between ${playerPokemon.name} (Lvl ${playerPokemon.level}) and ${wildPokemon.name} (Lvl ${wildPokemon.level})`);

    // Check if type effectiveness data is loaded
    if (!isTypeDataLoaded()) {
      logger.warn('Type effectiveness data not loaded yet, reloading...');
      // Try to load the data synchronously
      try {
        const xhr = new XMLHttpRequest();
        xhr.open('GET', './pokemon-types-battle.json', false); // false makes it synchronous
        xhr.send(null);
        if (xhr.status === 200) {
          typeEffectivenessData = JSON.parse(xhr.responseText);
          logger.debug('Type effectiveness data loaded synchronously');
        } else {
          logger.error(`Failed to load type effectiveness data: ${xhr.status}`);
        }
      } catch (e) {
        logger.error('Error loading type effectiveness data synchronously:', e);
      }
    }

    // Get Pokemon levels
    const playerLevel = playerPokemon.level || 1;
    const wildLevel = wildPokemon.level || 1;

    // Calculate type effectiveness
    const typeEffectiveness = calculateTypeEffectiveness(
      playerPokemon.types || ['Normal'],
      wildPokemon.types || ['Normal']
    );

    // Calculate damage based on level and type effectiveness
    const playerDamage = Math.round(playerLevel * typeEffectiveness.attackerToDefender.reduce((total, type) => total * type.multiplier, 1.0));

    // Calculate wild Pokemon's damage to player
    const wildTypeEffectiveness = calculateTypeEffectiveness(
      wildPokemon.types || ['Normal'],
      playerPokemon.types || ['Normal']
    );
    const wildDamage = Math.round(wildLevel * wildTypeEffectiveness.attackerToDefender.reduce((total, type) => total * type.multiplier, 1.0));

    // Determine the winner based on damage
    let playerWins;
    let resultMessage = '';

    // Get display names for result messages
    const wildPokemonDisplayName = getGermanPokemonName(wildPokemon);
    const playerPokemonDisplayName = getGermanPokemonName(playerPokemon);

    if (playerDamage > wildDamage) {
      // Player wins if their damage is higher
      playerWins = true;
      resultMessage = `Victory! ${wildPokemonDisplayName} has been defeated.`;
      logger.debug(`Battle calculation: Player wins with ${playerDamage} damage vs ${wildDamage} damage`);
    } else if (playerDamage < wildDamage) {
      // Wild Pokemon wins if its damage is higher
      playerWins = false;
      resultMessage = `Defeat! ${playerPokemonDisplayName} has lost the battle.`;
      logger.debug(`Battle calculation: Player loses with ${playerDamage} damage vs ${wildDamage} damage`);
    } else {
      // If damage is equal, randomly determine the winner
      playerWins = Math.random() >= 0.5; // 50% chance for player to win

      if (playerWins) {
        resultMessage = `Close battle! ${wildPokemonDisplayName} has been defeated in a tie.`;
      } else {
        resultMessage = `Close battle! ${playerPokemonDisplayName} has lost in a tie.`;
      }

      logger.debug(`Tie battle (${playerDamage} vs ${wildDamage}) resolved randomly: Player ${playerWins ? 'wins' : 'loses'}`);
    }

    // Log basic battle calculation details
    logToBattleConsole(`BATTLE CALC: ${playerPokemon.name} (Lvl ${playerLevel}) vs ${wildPokemon.name} (Lvl ${wildLevel})`);
    logToBattleConsole(`Types: [${playerPokemon.types}] vs [${wildPokemon.types}]`);

    // Calculate total multipliers
    const playerTotalMultiplier = typeEffectiveness.attackerToDefender.reduce((total, type) => total * type.multiplier, 1.0);
    const wildTotalMultiplier = wildTypeEffectiveness.attackerToDefender.reduce((total, type) => total * type.multiplier, 1.0);

    // Log damage calculations in a concise format
    logToBattleConsole(`DAMAGE CALC: Player ${playerLevel} × ${playerTotalMultiplier.toFixed(2)} = ${playerDamage} | Wild ${wildLevel} × ${wildTotalMultiplier.toFixed(2)} = ${wildDamage}`);

    // Log type effectiveness details
    let playerTypeDetails = [];
    typeEffectiveness.attackerToDefender.forEach(attackerType => {
        attackerType.defenderTypes.forEach(defenderEffect => {
            playerTypeDetails.push(`${attackerType.attackerType} vs ${defenderEffect.defenderType}: ×${defenderEffect.multiplier.toFixed(2)}`);
        });
    });
    logToBattleConsole(`PLAYER TYPE EFFECTS: ${playerTypeDetails.join(', ')}`);

    let wildTypeDetails = [];
    wildTypeEffectiveness.attackerToDefender.forEach(attackerType => {
        attackerType.defenderTypes.forEach(defenderEffect => {
            wildTypeDetails.push(`${attackerType.attackerType} vs ${defenderEffect.defenderType}: ×${defenderEffect.multiplier.toFixed(2)}`);
        });
    });
    logToBattleConsole(`WILD TYPE EFFECTS: ${wildTypeDetails.join(', ')}`);

    // Log the result
    logToBattleConsole(`RESULT: ${playerWins ? 'Player wins' : 'Wild Pokemon wins'} (${playerDamage} vs ${wildDamage})`);

    // Also log a summary to logger for browser console
    const battleDetails = `Battle formula details:
    - Player: ${playerPokemon.name} (Lvl ${playerLevel}) with types [${playerPokemon.types}]
    - Wild: ${wildPokemon.name} (Lvl ${wildLevel}) with types [${wildPokemon.types}]
    - Player damage = ${playerLevel} × ${playerTotalMultiplier.toFixed(2)} = ${playerDamage}
    - Wild damage = ${wildLevel} × ${wildTotalMultiplier.toFixed(2)} = ${wildDamage}
    - Result: ${playerWins ? 'Player wins' : 'Wild Pokemon wins'}`;

    logger.debug(battleDetails);


    // Check if it was a tie (equal damage)
    const wasTie = playerDamage === wildDamage;

    // Calculate experience points if player wins
    let experienceGained = 0;
    if (playerWins) {
      // Debug-Ausgabe vor der XP-Berechnung
      logger.debug(`Battle XP - About to calculate XP for defeating: ${wildPokemon.name}`);
      logger.debug(`Battle XP - Wild Pokemon details: Level=${wildLevel}, Rarity="${wildPokemon.rarity || 'common'}"`);
      logger.debug(`Battle XP - Wild Pokemon full object: ${JSON.stringify(wildPokemon, null, 2)}`);

      // Berechne die Erfahrungspunkte
      experienceGained = getExpFromBattle(wildLevel, wildPokemon.rarity || 'common');

      // Ausgabe der Erfahrungspunkte
      logToBattleConsole(`EXP GAINED: ${experienceGained} experience points`);
      logger.debug(`Battle rewards: ${experienceGained} experience points from defeating ${wildPokemon.name} (Lvl ${wildLevel}, ${wildPokemon.rarity || 'common'})`);

      // Überprüfe, ob die Erfahrungspunkte korrekt berechnet wurden
      const expectedXP = Math.floor(30 * wildLevel * (RARITY_MULTIPLIERS[wildPokemon.rarity] || 1.0));
      logger.debug(`Battle XP - Expected XP calculation: 30 × ${wildLevel} × ${RARITY_MULTIPLIERS[wildPokemon.rarity] || 1.0} = ${expectedXP}`);

      if (experienceGained !== expectedXP) {
        logger.warn(`Battle XP - Discrepancy detected! Calculated: ${experienceGained}, Expected: ${expectedXP}`);
      }
    }
    // For test battles (with test-pikachu-1), always simulate XP gain even if player loses
    else if (playerPokemon.id === 'test-pikachu-1') {
      // Force player to win for test battles
      playerWins = true;
      resultMessage = `Test Victory! ${wildPokemonDisplayName} has been defeated.`;

      // Calculate simulated XP for test battles
      experienceGained = Math.floor(30 * wildLevel * (RARITY_MULTIPLIERS[wildPokemon.rarity] || 1.0));

      // Log the simulated XP gain
      logToBattleConsole(`TEST MODE: Simulated EXP GAINED: ${experienceGained} experience points`);
      logger.debug(`Test battle: Simulated ${experienceGained} experience points from defeating ${wildPokemon.name}`);
    }

    return {
      playerWins,
      resultMessage,
      playerPokemon,
      wildPokemon,
      wasTie,
      typeEffectiveness,
      playerDamage,
      wildDamage,
      playerTypeMultiplier: typeEffectiveness.attackerToDefender.reduce((total, type) => total * type.multiplier, 1.0).toFixed(2),
      wildTypeMultiplier: wildTypeEffectiveness.attackerToDefender.reduce((total, type) => total * type.multiplier, 1.0).toFixed(2),
      experienceGained
    };
  } catch (error) {
    logger.error('Error calculating battle outcome:', error);
    return {
      playerWins: false,
      resultMessage: 'Battle calculation error!',
      error: error.message
    };
  }
}

/**
 * Get CSS class for battle result
 * @param {boolean} playerWins - Whether the player won the battle
 * @param {boolean} wasTie - Whether the battle was a tie
 * @returns {string} - CSS class for styling
 */
export function getBattleResultClass(playerWins, wasTie = false) {
  if (wasTie) {
    return playerWins ? 'battle-tie-victory' : 'battle-tie-defeat';
  }
  return playerWins ? 'battle-victory' : 'battle-defeat';
}

/**
 * Battle calculator service
 */
export class BattleCalculator {
  constructor() {
    // For future expansion with more complex battle mechanics
  }

  /**
   * Calculate battle outcome
   * @param {Object} playerPokemon - The player's Pokemon
   * @param {Object} wildPokemon - The wild Pokemon
   * @returns {Object} - The battle result
   */
  calculateOutcome(playerPokemon, wildPokemon) {
    return calculateBattleOutcome(playerPokemon, wildPokemon);
  }
}

// Export a singleton instance
export const battleCalculator = new BattleCalculator();
