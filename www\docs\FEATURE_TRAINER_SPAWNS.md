Rolle:
Du bist ein Code-Augmentierungs-Agent für eine Capacitor-basierte Hybrid-App mit Browser-Fallback. Erweitere und repariere den bestehenden TrainerSpawner so, dass 5 NPC-Trainer innerhalb eines 500m-Radius um die Spielerposition auf realen OpenStreetMap-Wegen spawnen. Nutze die bestehende Overpass-Infrastruktur der App (z.B. overpass-landuse.js) für Fetch, Caching und Rate-Limiting. Außerdem: Sobald der Spieler seinen letzten initialen Spawn-Referenzpunkt um mehr als 250m verlassen hat, sollen erneut 5 Trainer gespawnt werden (analog zur bestehenden Pokemon-Spawn-Logik). Dies nur erwähnen und technisch andocken, nicht im Detail ausarbeiten.

Kontext zur Laufzeit:

    Native Capacitor: direkte Overpass-Requests (CORS irrelevant).

    Browser-Fallback: Standard-CORS, optionaler Proxy möglich, aber nicht erforderlich.

    Es existiert bereits ein Overpass-Client-Pattern in www/overpass-landuse.js mit POST an https://overpass-api.de/api/interpreter, Content-Type text/plain, sowie Caching/Rate-Limits. Diese Architektur wiederverwenden.

Ziel:

    Statt eines einzelnen Trainers sollen 5 Trainer in einem 500m-Radius spawnen.

    Spawnpunkte müssen auf Wegen/Trails/Straßen liegen (keine Flächen).

    Overpass/OSM-Daten verwenden, inkl. robustem Caching, Fallback.

    Repariere services/trainer-spawner.js (Blöcke, HTML-Entities, Klammern).

    Respawn-Trigger bei >250m vom letzten Spawn-Referenzpunkt erwähnen und an bestehenden Mechanismus andocken.

Dateien/Abhängigkeiten:

    services/trainer-spawner.js (gelieferte fehlerhafte Version fixen und erweitern)

    turf aus ../lib/turf.js

    gameState.pokedexData, Trainer, Pokemon, logger

    Wiederverwendung Overpass-Infra (analog www/overpass-landuse.js): Fetch-Wrapper, Cache, Rate-Limit

Funktionale Anforderungen:

    Multipler Spawn

    Pro Zyklus 5 Trainer innerhalb 500m um (playerLat, playerLng).

    Mindestabstand minSpacing=20m zwischen Spawnpunkten; keine Duplikate.

    Hinweis: Einbinden in bestehendes Distanz-Triggering, sodass bei >250m vom letzten Spawn-Referenzpunkt 5 neue Trainer gespawnt werden (analog zu Pokémon). Nur Schnittstelle/Hook vorsehen (z.B. lastTrainerSpawnAnchor), keine detaillierte Implementierung.

    Overpass/OSM-Integration

    Implementiere fetchOSMWays(lat, lng, radiusMeters=500) unter Wiederverwendung der vorhandenen Overpass-Architektur:

        POST an https://overpass-api.de/api/interpreter, Content-Type text/plain.

        Caching/Rate-Limiting analog www/overpass-landuse.js, aber eigener Namespace/Key (z.B. “osm-ways:<roundedLatLng>:<radius>”).

        Cache-Dauer: 60 Minuten (an Landuse angeglichen).

        MAX_CACHE_ENTRIES_OSM_WAYS: z.B. 200 (separat vom Landuse-Cache).

    Overpass-Ql Query (out:json; out geom), Motorways/Trunks filtern, Fuß-/Wanderwege priorisieren:

        [out:json][timeout:25];
        (
        way(around:500, <lat>, <lng>)["highway"]["highway"!="motorway"]["highway"!="trunk"];
        way(around:500, <lat>, <lng>)["highway"~"path|footway|pedestrian|residential|service|track|living_street|steps"];
        way(around:500, <lat>, <lng>)["cycleway"];
        relation(around:500, <lat>, <lng>)["type"="route"]["route"~"hiking|foot|bicycle"];
        );
        out geom;

    Sampling auf Wegen mit Gewichtung

    Transformiere Overpass-Ergebnisse in LineString/MultiLineString-Features.

    Längenproportionales Sampling mit Tag-Gewichtung:

        Berechne turf.length je LineString.

        Gewichte Linien nach Tag-Priorität: foot/pedestrian/hiking/cycleway stärker als residential/service/track.

        Standard-Gewichte (überschreibbar via setTrainerTagWeights):

            footway/path/pedestrian/steps/hiking routes: Faktor 3

            cycleway/bicycle routes: Faktor 2

            residential/service/track/living_street: Faktor 1

            primary/secondary/tertiary (falls durch Filter noch enthalten): Faktor 0.5 oder exkludieren

    Ziehe Zufallspunkte proportional zur gewichteten Netzlänge: zufällige Distanz im kumulierten Längenraum, dann turf.along auf der gewählten Linie.

    Snap-Schutz: turf.nearestPointOnLine auf die gewählte Linie.

    Prüfe Distanz zum Spieler ≤ 500m; sonst verwerfen und neu sampeln.

    minSpacing=20m zwischen Punkten; bei zu wenig Ergebnissen erneut sampeln oder minSpacing schrittweise bis min.10m reduzieren.

    Fallbacks

    Keine Wege/Relationen gefunden oder Overpass-Fehler/Timeout:

        Fallback: alter random Offset innerhalb 500m, anschließend versuchen auf verfügbare Linien zu snappen.

        Wenn gar nichts: Fallback-Positionen ohne Snap akzeptieren.

    Max. 2 Retries mit Exponential Backoff (z.B. 300ms, 900ms), danach Fallback.

    logger.warn bei jedem Fallback.

    API/Infra/Caching

    Wiederverwendung Fetch-/Cache-Pattern wie www/overpass-landuse.js:

        60 Minuten Cache-Dauer.

        Eigener Namespace/Key (z.B. Geohash 7 oder lat/lng auf 5 Dezimalstellen runden).

        Separate Cache-Größe (MAX_CACHE_ENTRIES_OSM_WAYS=200).

    Browser-Fallback ohne harten Proxy-Zwang.

    Integration in TrainerSpawner

    Repariere services/trainer-spawner.js:

        Schließe offene try/catch, Blöcke, Klammern.

        Ersetze HTML-Entities (<, >) durch echte Operatoren.

        async/await korrekt, Fehlerpfade sauber.

    Neue/erweiterte Methoden:

        async loadData()

        buildOverpassQuery(lat, lng, radius)

        async fetchOSMWays(lat, lng, radius)

        toLineStrings(overpassJson)

        samplePointsOnLines(lineStrings, neededCount, minSpacingMeters, centerLat, centerLng, radiusMeters, tagWeights)

        snapPointToLine(point, lineStrings)

        generateOSMConstrainedPositions(centerLat, centerLng, count=5, radius=500, minSpacing=20)

        generateRandomPosition(centerLat, centerLng, radius=this.spawnRadius) als Fallback

        async spawnRandomTrainers(playerLat, playerLng, count=5, options?): nutzt OSM-Positionen und erzeugt Trainer-Instanzen, gibt Array zurück.

        spawnRandomTrainer(positionOverride?): refactor, um vorgegebene Positionen zu akzeptieren.

        NEU: setTrainerTagWeights(config) – kompakte Hilfsfunktion nur für NPC-Trainer-Gewichtungen (nicht für Pokémon).

    Performance

    Overpass nur beim Spawn-Vorgang, danach Cache nutzen, wenn Spieler im selben Center-Raster bleibt.

    Timeout 25s, saubere Abbruchpfade.

    Konfiguration

    spawnRadius Default 500m, desiredTrainerCount Default 5, minSpacing Default 20m.

    Cache-Dauer 60min (OSM-Ways) und MAX_CACHE_ENTRIES überschreibbar via Config.

    Tag-Gewichte überschreibbar via setTrainerTagWeights(config).

    Hinweis: Anschluss an bestehenden “>250m seit letztem Spawn-Referenzpunkt”-Mechanismus; nur Hook/Option vorsehen (z.B. this.lastTrainerSpawnAnchor).

    Logging/Tests

    logger.debug: Anzahl Wege/Relationen, Gesamtlänge, angewandte Gewichte, Sampling-Verteilung, finale Koordinaten.

    logger.warn: Fallbacks, Overpass-Fehler/Timeouts, unzureichende valide Punkte (inkl. adaptive minSpacing-Anpassung).

    Smoke-Test: 5 Trainer erzeugen, Abstände ≥ minSpacing, Distanz ≤ radius, Logausgabe prüfen.

Edge Cases:

    Dünnes Wegenetz: minSpacing graduell bis 10m reduzieren.

    Viele kurze Segmente: Längenproportionales Sampling mit Gewichtung beibehalten, kein Bias zu Segmentanzahl.

    Großstadt: highway=motorway/trunk ausgeschlossen.

    Koordinatenordnung: turf [lng, lat], setPosition erwartet (lat, lng) – konsequent konvertieren.

Abgabe:

    Vollständig reparierte und erweiterte services/trainer-spawner.js.

    Kommentarblock (README-Notizen) am Dateianfang:

        Overpass-Nutzung, Query, Caching/Rate-Limit (60min), Konfiguration, Fallback-Strategie, 250m-Respawn-Hinweis.

    Usage-Hinweis:

        const trainers = await trainerSpawner.spawnRandomTrainers(playerLat, playerLng, 5);

        Rückgabe: Array aus 5 Trainer-Instanzen mit gesetzter Position.

Optionale Verbesserungen:

    Kreuzungsabstand (z.B. >5m von Knoten mit degree>2).

    Feinere Gewichtung je highway=* Subtypen.

    Serverseitiger Overpass-Proxy optional (nicht nötig in Capacitor).

Implementierungsdetails – neue/aktualisierte Methoden (Skizze in Code-Form):

    setTrainerTagWeights(config)
    Aufgabe: Stellt die Gewichtungen für NPC-Trainer-Spawn auf OSM-Tags zentral ein, unabhängig von Pokémon-Logik.

    Beispiel-Implementierung:

        Default-Gewichte:

            footPriority: 3 (footway, path, pedestrian, steps, hiking relations)

            cyclePriority: 2 (cycleway, bicycle relations)

            localRoadPriority: 1 (residential, service, track, living_street)

            primaryRoadPriority: 0.5 (primary/secondary/tertiary – optional; standardmäßig kaum bis gar nicht genutzt)

        Methode validiert Eingaben (>=0), merged mit Defaults und speichert in this.trainerTagWeights.

    Beispiel-Code:

        function setTrainerTagWeights(config = {}) {
        const defaults = {
        footPriority: 3,
        cyclePriority: 2,
        localRoadPriority: 1,
        primaryRoadPriority: 0.5
        };
        const cleaned = {
        footPriority: Number.isFinite(config.footPriority) && config.footPriority >= 0 ? config.footPriority : defaults.footPriority,
        cyclePriority: Number.isFinite(config.cyclePriority) && config.cyclePriority >= 0 ? config.cyclePriority : defaults.cyclePriority,
        localRoadPriority: Number.isFinite(config.localRoadPriority) && config.localRoadPriority >= 0 ? config.localRoadPriority : defaults.localRoadPriority,
        primaryRoadPriority: Number.isFinite(config.primaryRoadPriority) && config.primaryRoadPriority >= 0 ? config.primaryRoadPriority : defaults.primaryRoadPriority
        };
        this.trainerTagWeights = cleaned;
        return this.trainerTagWeights;
        }

    Verwendung der Gewichte im Sampling:

        In samplePointsOnLines(..., tagWeights = this.trainerTagWeights || defaults):

            Bestimme für jede LineString den Tag-Typ:

                Falls tags.highway in ["footway","path","pedestrian","steps"] → weight = tagWeights.footPriority

                Falls tags.cycleway vorhanden oder highway == "cycleway" → weight = tagWeights.cyclePriority

                Falls highway in ["residential","service","track","living_street"] → weight = tagWeights.localRoadPriority

                Optional: highway in ["primary","secondary","tertiary"] → weight = tagWeights.primaryRoadPriority

                Falls Relation route in ["hiking","foot"] → weight = max(weight, tagWeights.footPriority)

                Falls Relation route == "bicycle" → weight = max(weight, tagWeights.cyclePriority)

            Effektive Länge = turf.length(line) * weight

            In die kumulative Verteilung einfließen lassen.

    buildOverpassQuery(lat, lng, radius):

        Nutzt die Query aus den Anforderungen (mit out geom, Filter für motorways/trunks).

    fetchOSMWays(lat, lng, radius):

        POST an Overpass, Cache-Key “osm-ways:<roundedLat>:<roundedLng>:<radius>”, Cache 60min, MAX 200, 2 Retries mit Backoff.

    toLineStrings(overpassJson):

        Extrahiere way- und relation-Geometrien als LineString/MultiLineString, inklusive tags.

    snapPointToLine(point, lineStrings):

        turf.nearestPointOnLine je Linie, wähle minimalen Abstand.

    generateOSMConstrainedPositions(centerLat, centerLng, count=5, radius=500, minSpacing=20):

        Lädt Wege (Cache beachten), sampelt Punkte mit Gewichtung, dedupliziert via minSpacing, fallback bei Bedarf.

    spawnRandomTrainers(playerLat, playerLng, count=5, options?):

        await loadData()

        Positionen via generateOSMConstrainedPositions

        Für jede Position spawnRandomTrainer({ lat, lng })

        Rückgabe Array von Trainern

        Optionaler Hook: Aktualisiere this.lastTrainerSpawnAnchor = {lat, lng} und Export eines Prüfers shouldRespawnTrainers(currentLat, currentLng, anchor, thresholdMeters=250) der analog zu Pokémon-Flow genutzt werden kann.

    generateRandomPosition(centerLat, centerLng, radius):

        Bestehende Methode als Fallback behalten.

    README-Kommentarblock am Dateianfang:

        Beschreibe Overpass-Nutzung, Query, Caching (60min), Konfigurationsoptionen (spawnRadius, desiredTrainerCount, minSpacing, setTrainerTagWeights), Fallback-Strategie, 250m-Respawn-Hinweis.

Akzeptanzkriterien:

    5 Trainer spawnen auf OSM-Wegen innerhalb 500m; Min-Abstand 20m.

    Overpass-Daten werden 60min gecacht; Requests folgen dem bestehenden Muster.

    Gewichtete Verteilung bevorzugt Fuß-/Wanderwege.

    Fallbacks funktionieren robust und loggen Warnungen.

    Datei services/trainer-spawner.js ist syntaktisch korrekt und integriert alle neuen Methoden, inkl. setTrainerTagWeights speziell für NPC-Trainer.
