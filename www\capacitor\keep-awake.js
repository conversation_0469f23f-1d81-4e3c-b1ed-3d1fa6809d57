// capacitor/keep-awake.js
// Helper module for Capacitor Keep Awake plugin

import { logger } from '../utils/logger.js';

/**
 * Keep the screen awake
 * @returns {Promise<boolean>} - Whether the operation was successful
 */
export async function keepScreenAwake() {
  try {
    // Check if Capacitor and the plugin are available
    if (window.Capacitor?.Plugins?.KeepAwake) {
      await window.Capacitor.Plugins.KeepAwake.keepAwake();
      logger.info('Screen will stay awake');
      return true;
    } else {
      logger.warn('KeepAwake plugin not available');
      return false;
    }
  } catch (e) {
    logger.error('Error keeping screen awake:', e);
    return false;
  }
}

/**
 * Allow the screen to sleep
 * @returns {Promise<boolean>} - Whether the operation was successful
 */
export async function allowScreenSleep() {
  try {
    // Check if Capacitor and the plugin are available
    if (window.Capacitor?.Plugins?.KeepAwake) {
      await window.Capacitor.Plugins.KeepAwake.allowSleep();
      logger.info('Screen can sleep now');
      return true;
    } else {
      logger.warn('KeepAwake plugin not available');
      return false;
    }
  } catch (e) {
    logger.error('Error allowing screen to sleep:', e);
    return false;
  }
}

// Note: isScreenKeptAwake() function removed as it was unused
