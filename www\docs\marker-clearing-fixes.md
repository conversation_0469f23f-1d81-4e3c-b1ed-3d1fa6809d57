# Marker Clearing Contract Violation Fixes

## Overview
This document describes the fixes implemented to address two critical issues with marker collection clearing in the MapRenderer service:

1. **Comment 1**: Collections could not be empty after Clear - Call sites that assume empty marker collections after clearing
2. **Comment 2**: Entries without `entry.marker` remain permanently in Collections - Invalid entries accumulate over time

## Issues Identified

### Issue 1: Contract Violation - Collections Not Empty After Clear
**Problem**: Code calling `clearAllPokemonMarkers()` or `clearAllTrainerMarkers()` assumed the collections would be completely empty afterward, but this wasn't guaranteed when:
- The map instance was not available (`map` is null/undefined)
- Entries existed without valid `marker` properties
- Map removal operations failed

**Impact**: This could cause rendering issues, memory leaks, and unexpected behavior in code that relied on empty collections.

### Issue 2: Invalid Entries Accumulate
**Problem**: Entries without a `marker` property (or with null/undefined markers) were never removed from collections, causing them to grow indefinitely.

**Impact**: Memory leaks and degraded performance over time.

## Solutions Implemented

### Enhanced `_clearMarkerCollection` Method
The core clearing method was enhanced with:

1. **Optional Invalid Entry Cleanup**: New `cleanupInvalidEntries` parameter allows removal of entries without markers
2. **Detailed Statistics**: Returns comprehensive stats about the clearing operation
3. **Contract Violation Detection**: Warns when collections aren't fully cleared
4. **Robust Error Handling**: Continues processing even if individual marker removal fails

```javascript
_clearMarkerCollection(collection, map, collectionName, cleanupInvalidEntries = false) {
    // Enhanced implementation with statistics and cleanup options
    return {
        removedCount,           // Markers successfully removed from map
        invalidEntriesCount,    // Invalid entries cleaned up
        remainingCount,         // Entries still in collection
        wasFullyCleared        // Boolean indicating complete clearing
    };
}
```

### Updated Public Methods
Both `clearAllPokemonMarkers()` and `clearAllTrainerMarkers()` now:
- Accept optional `cleanupInvalidEntries` parameter
- Return detailed statistics about the clearing operation
- Provide fallback statistics on error

### Call Site Updates
All call sites were updated to:
- Use the cleanup option where appropriate
- Handle cases where collections might not be fully cleared
- Log warnings when contract violations occur
- Provide fallback behavior for incomplete clearing

## Key Changes Made

### 1. Enhanced Core Method (`www/services/map-renderer.js`)
- Added `cleanupInvalidEntries` parameter to `_clearMarkerCollection`
- Implemented tracking of invalid entries
- Added comprehensive statistics return object
- Enhanced logging and warning system

### 2. Updated Public Clearing Methods
- `clearAllPokemonMarkers(cleanupInvalidEntries = false)`
- `clearAllTrainerMarkers(cleanupInvalidEntries = false)`
- `clearAllMarkers(cleanupInvalidEntries = true)` - defaults to cleanup for master reset

### 3. Call Site Improvements (`www/main.js`)
- `handleStoredPokemonPositioning`: Uses cleanup and checks for incomplete clearing
- `clearAllPokemon`: Uses cleanup and logs warnings for remaining entries
- `clearAllTrainers`: Uses cleanup and logs warnings for remaining entries

### 4. FabManager Integration (`www/ui/FabManager.js`)
- `clearAllSpawnsAndRespawn`: Uses MapRenderer method instead of manual clearing
- Provides fallback for when MapRenderer is unavailable
- Force clears remaining entries during complete resets

## Testing
A comprehensive test suite was created (`www/tests/marker-clearing-test.html`) that verifies:
1. Normal clearing with map available
2. Contract violation detection when map unavailable
3. Invalid entry cleanup functionality
4. Mixed scenarios with valid, invalid, and failing entries

## Benefits

### 1. Contract Compliance
- Call sites can now detect when collections aren't fully cleared
- Appropriate warnings and fallback behavior implemented
- No more silent contract violations

### 2. Memory Management
- Invalid entries can be cleaned up to prevent memory leaks
- Collections can be fully cleared when needed
- Better resource management over time

### 3. Debugging and Monitoring
- Detailed statistics help identify clearing issues
- Comprehensive logging for troubleshooting
- Clear warnings when contract violations occur

### 4. Backward Compatibility
- All changes are backward compatible
- Default behavior preserved for existing code
- Optional parameters allow gradual adoption

## Usage Examples

### Basic Clearing (Backward Compatible)
```javascript
mapRenderer.clearAllPokemonMarkers(); // Works as before
```

### Enhanced Clearing with Cleanup
```javascript
const stats = mapRenderer.clearAllPokemonMarkers(true);
if (!stats.wasFullyCleared) {
    logger.warn(`${stats.remainingCount} entries remain in collection`);
}
```

### Complete Reset with Fallback
```javascript
const stats = mapRenderer.clearAllMarkers(true);
if (!stats.wasFullyCleared) {
    // Force clear for complete reset scenarios
    gameState.pokemonMarkers?.clear();
    gameState.trainerMarkers?.clear();
}
```

## Conclusion
These fixes address both identified issues while maintaining backward compatibility and providing enhanced functionality for better resource management and debugging. The implementation follows defensive programming principles and provides clear feedback about clearing operations.
