// battle-utils-consolidation-test.js
// Test for consolidated battle utility functions

import { animateExpBar, showLevelUpNotification, showNotification, calculateExpProgress } from '../utils/battle-utils.js';

/**
 * Create a mock DOM container for testing
 */
function createMockContainer() {
  const container = document.createElement('div');
  container.innerHTML = `
    <div class="pokemon-exp-container">
      <div class="pokemon-exp-bar">
        <div class="pokemon-exp-fill" style="width: 50%;"></div>
        <div class="pokemon-exp-new" style="width: 0%; opacity: 0;"></div>
      </div>
      <div class="pokemon-exp-text">50/100 XP</div>
    </div>
  `;
  document.body.appendChild(container);
  return container;
}

/**
 * Create test Pokemon data
 */
function createTestPokemon() {
  return {
    name: '<PERSON><PERSON><PERSON>',
    level: 5,
    experience: 125,
    rarity: 'starter',
    dex_number: 25
  };
}

/**
 * Test animateExpBar function
 */
async function testAnimateExpBar() {
  console.log('=== Testing animateExpBar Function ===');
  
  try {
    const container = createMockContainer();
    const pokemon = createTestPokemon();
    const expProgress = calculateExpProgress(pokemon);
    const expGained = 50;
    
    console.log('Test Pokemon:', pokemon);
    console.log('Experience Progress:', expProgress);
    console.log('Experience Gained:', expGained);
    
    // Test the animation function
    animateExpBar(container, expProgress, expGained);
    
    // Check if elements were modified
    const expFill = container.querySelector('.pokemon-exp-fill');
    const expNew = container.querySelector('.pokemon-exp-new');
    const expText = container.querySelector('.pokemon-exp-text');
    
    if (expFill && expNew && expText) {
      console.log('✅ animateExpBar: DOM elements found and function executed');
      
      // Wait for animation to start
      setTimeout(() => {
        const newText = expText.textContent;
        console.log('Updated XP text:', newText);
        
        if (newText.includes('XP')) {
          console.log('✅ animateExpBar: XP text updated correctly');
        } else {
          console.log('❌ animateExpBar: XP text not updated');
        }
      }, 100);
    } else {
      console.log('❌ animateExpBar: Required DOM elements not found');
    }
    
    // Clean up
    document.body.removeChild(container);
    
  } catch (error) {
    console.error('❌ animateExpBar test failed:', error);
  }
}

/**
 * Test showNotification function
 */
async function testShowNotification() {
  console.log('\n=== Testing showNotification Function ===');
  
  try {
    const container = createMockContainer();
    const message = 'Test notification message';
    const className = 'test-notification';
    const duration = 1000; // Short duration for testing
    
    console.log('Testing notification with message:', message);
    
    // Test the notification function
    const promise = showNotification(container, message, className, duration);
    
    // Check if notification was created
    setTimeout(() => {
      const notification = container.querySelector(`.${className}`);
      if (notification) {
        console.log('✅ showNotification: Notification element created');
        console.log('Notification text:', notification.textContent);
        
        if (notification.textContent === message) {
          console.log('✅ showNotification: Message content correct');
        } else {
          console.log('❌ showNotification: Message content incorrect');
        }
      } else {
        console.log('❌ showNotification: Notification element not found');
      }
    }, 50);
    
    // Wait for notification to complete
    await promise;
    console.log('✅ showNotification: Promise resolved successfully');
    
    // Clean up
    document.body.removeChild(container);
    
  } catch (error) {
    console.error('❌ showNotification test failed:', error);
  }
}

/**
 * Test showLevelUpNotification function
 */
async function testShowLevelUpNotification() {
  console.log('\n=== Testing showLevelUpNotification Function ===');
  
  try {
    const container = createMockContainer();
    const pokemon = createTestPokemon();
    const oldLevel = 4;
    const newLevel = 5;
    
    console.log('Testing level up notification for:', pokemon.name);
    console.log(`Level up: ${oldLevel} -> ${newLevel}`);
    
    // Test the level up notification function
    const promise = showLevelUpNotification(container, pokemon, oldLevel, newLevel);
    
    // Check if notification was created
    setTimeout(() => {
      const notification = container.querySelector('.level-up-notification');
      if (notification) {
        console.log('✅ showLevelUpNotification: Level up notification created');
        console.log('Notification text:', notification.textContent);
        
        if (notification.textContent.includes('Level')) {
          console.log('✅ showLevelUpNotification: Level up message correct');
        } else {
          console.log('❌ showLevelUpNotification: Level up message incorrect');
        }
      } else {
        console.log('❌ showLevelUpNotification: Level up notification not found');
      }
    }, 50);
    
    // Wait for notification to complete
    await promise;
    console.log('✅ showLevelUpNotification: Promise resolved successfully');
    
    // Clean up
    document.body.removeChild(container);
    
  } catch (error) {
    console.error('❌ showLevelUpNotification test failed:', error);
  }
}

/**
 * Test edge cases
 */
async function testEdgeCases() {
  console.log('\n=== Testing Edge Cases ===');
  
  try {
    // Test with null container
    console.log('Testing with null container...');
    animateExpBar(null, {}, 50);
    console.log('✅ animateExpBar handles null container gracefully');
    
    await showNotification(null, 'test', 'test', 1000);
    console.log('✅ showNotification handles null container gracefully');
    
    await showLevelUpNotification(null, createTestPokemon(), 1, 2);
    console.log('✅ showLevelUpNotification handles null container gracefully');
    
    // Test with invalid Pokemon data
    console.log('Testing with invalid Pokemon data...');
    const container = createMockContainer();
    animateExpBar(container, calculateExpProgress(null), 50);
    console.log('✅ animateExpBar handles invalid Pokemon data gracefully');
    
    document.body.removeChild(container);
    
  } catch (error) {
    console.error('❌ Edge case test failed:', error);
  }
}

/**
 * Run all tests
 */
async function runAllTests() {
  console.log('🧪 Starting Battle Utils Consolidation Tests\n');
  
  await testAnimateExpBar();
  await new Promise(resolve => setTimeout(resolve, 200)); // Small delay between tests
  
  await testShowNotification();
  await new Promise(resolve => setTimeout(resolve, 200));
  
  await testShowLevelUpNotification();
  await new Promise(resolve => setTimeout(resolve, 200));
  
  await testEdgeCases();
  
  console.log('\n🎉 All Battle Utils Consolidation Tests Completed!');
  console.log('✅ Functions successfully consolidated from BattleScreen.js and TrainerBattleScreen.js to battle-utils.js');
}

// Export for use in other test files
export { runAllTests, testAnimateExpBar, testShowNotification, testShowLevelUpNotification };

// Run tests if this file is executed directly
if (typeof window !== 'undefined' && window.location) {
  document.addEventListener('DOMContentLoaded', () => {
    runAllTests();
  });
}
