// services/experience-system.js
// Service for managing Pokemon experience points and leveling

import { logger } from '../utils/logger.js';

// Rarity-dependent EXP curves mapping
const EXP_CURVES_BY_RARITY = {
  mythical:   'slow',         // Very slow growth - endgame monsters
  legendary:  'medium_slow',  // Slow growth - strong but trainable
  starter:    'medium_fast',  // Balanced - player-friendly
  rare:       'parabolic',    // Medium curve - challenge with reward
  scarce:     'fast',         // Fast growth - alternatives to starters
  common:     'fast',         // Very fast growth - early game phase
};

// Rarity multipliers for battle experience
const RARITY_MULTIPLIERS = {
  mythical:   3.0,
  legendary:  2.5,
  starter:    1.8,
  rare:       1.5,
  scarce:     1.2,
  common:     1.0
};

// Base experience value for battles
const BASE_BATTLE_EXP = 30;

/**
 * Calculate the total experience required for a specific level based on the growth curve
 * @param {number} level - The target level
 * @param {string} curve - The growth curve type ('fast', 'medium_fast', 'medium_slow', 'slow', 'parabolic')
 * @returns {number} - The total experience required
 */
export function getExpForLevel(level, curve) {
  switch (curve) {
    case 'fast':
      return Math.floor((4 * Math.pow(level, 3)) / 5);
    case 'medium_fast':
      return Math.pow(level, 3);
    case 'medium_slow':
      return Math.floor((6 / 5) * Math.pow(level, 3) - 15 * Math.pow(level, 2) + 100 * level - 140);
    case 'slow':
      return Math.floor((5 * Math.pow(level, 3)) / 4);
    case 'parabolic':
      return Math.floor(1.2 * Math.pow(level, 3) - 10 * Math.pow(level, 2) + 90 * level);
    default:
      logger.error('Unknown EXP curve:', curve);
      // Default to medium_fast if unknown curve
      return Math.pow(level, 3);
  }
}

/**
 * Get the experience curve for a Pokemon based on its rarity
 * @param {string} rarity - The Pokemon's rarity
 * @returns {string} - The experience curve type
 */
export function getExpCurveForRarity(rarity) {
  if (!rarity || !EXP_CURVES_BY_RARITY[rarity]) {
    logger.debug(`Unknown rarity "${rarity}", defaulting to "common" curve`);
    return EXP_CURVES_BY_RARITY.common;
  }
  return EXP_CURVES_BY_RARITY[rarity];
}

/**
 * Calculate experience gained from defeating a Pokemon in battle
 * @param {number} enemyLevel - The defeated Pokemon's level
 * @param {string} enemyRarity - The defeated Pokemon's rarity
 * @returns {number} - The experience points gained
 */
export function getExpFromBattle(enemyLevel, enemyRarity) {
  // Debug-Ausgabe der Eingabeparameter
  logger.debug(`XP Calculation - Input parameters: enemyLevel=${enemyLevel}, enemyRarity="${enemyRarity}"`);
  logger.debug(`XP Calculation - BASE_BATTLE_EXP constant value: ${BASE_BATTLE_EXP}`);

  // Default to common if rarity is unknown
  const rarityMultiplier = RARITY_MULTIPLIERS[enemyRarity] || RARITY_MULTIPLIERS.common;

  // Debug-Ausgabe aller verfügbaren Multiplikatoren
  logger.debug(`XP Calculation - Available rarity multipliers: ${JSON.stringify(RARITY_MULTIPLIERS)}`);
  logger.debug(`XP Calculation - Selected multiplier for "${enemyRarity}": ${rarityMultiplier}`);

  // Calculate experience points
  const baseTimesLevel = BASE_BATTLE_EXP * enemyLevel;
  const expGained = Math.floor(baseTimesLevel * rarityMultiplier);

  // Detaillierte Berechnung ausgeben
  logger.debug(`XP Calculation - Formula: BASE_BATTLE_EXP × enemyLevel × rarityMultiplier`);
  logger.debug(`XP Calculation - Step 1: ${BASE_BATTLE_EXP} × ${enemyLevel} = ${baseTimesLevel}`);
  logger.debug(`XP Calculation - Step 2: ${baseTimesLevel} × ${rarityMultiplier} = ${baseTimesLevel * rarityMultiplier}`);
  logger.debug(`XP Calculation - Step 3 (Math.floor): Math.floor(${baseTimesLevel * rarityMultiplier}) = ${expGained}`);
  logger.debug(`XP Calculation - Final result: ${expGained} experience points`);

  return expGained;
}

/**
 * Calculate the level for a given amount of experience and growth curve
 * @param {number} exp - The current experience points
 * @param {string} curve - The growth curve type
 * @returns {number} - The corresponding level
 */
export function getLevelForExp(exp, curve) {
  // Start from level 1 and increase until we find the right level
  let level = 1;

  while (getExpForLevel(level + 1, curve) <= exp) {
    level++;

    // Cap at level 100 to prevent infinite loops
    if (level >= 100) break;
  }

  return level;
}

/**
 * Calculate experience needed for the next level
 * @param {number} currentLevel - The current level
 * @param {string} curve - The growth curve type
 * @returns {number} - Experience needed for the next level
 */
export function getExpNeededForNextLevel(currentLevel, curve) {
  const currentLevelExp = getExpForLevel(currentLevel, curve);
  const nextLevelExp = getExpForLevel(currentLevel + 1, curve);
  return nextLevelExp - currentLevelExp;
}

/**
 * Calculate the progress percentage to the next level
 * @param {number} currentExp - Current total experience
 * @param {number} currentLevel - Current level
 * @param {string} curve - The growth curve type
 * @returns {number} - Percentage progress to next level (0-100)
 */
export function getExpProgressPercentage(currentExp, currentLevel, curve) {
  const currentLevelExp = getExpForLevel(currentLevel, curve);
  const nextLevelExp = getExpForLevel(currentLevel + 1, curve);
  const expInCurrentLevel = currentExp - currentLevelExp;
  const expNeededForNextLevel = nextLevelExp - currentLevelExp;

  return Math.floor((expInCurrentLevel / expNeededForNextLevel) * 100);
}

/**
 * Experience system service
 */
export class ExperienceSystem {
  constructor() {
    // For future expansion
  }

  /**
   * Get the experience curve for a Pokemon
   * @param {Object} pokemon - The Pokemon object
   * @returns {string} - The experience curve type
   */
  getExpCurve(pokemon) {
    return getExpCurveForRarity(pokemon.rarity);
  }

  /**
   * Add experience to a Pokemon and handle level up
   * @param {Object} pokemon - The Pokemon object to update
   * @param {number} expAmount - The amount of experience to add
   * @returns {Object} - Result with updated Pokemon and level up info
   */
  addExperience(pokemon, expAmount) {
    // Initialize experience if not present
    if (typeof pokemon.experience !== 'number') {
      pokemon.experience = this.getInitialExpForLevel(pokemon.level || 1, pokemon.rarity);
      // logger.debug(`Initialized ${pokemon.name} with ${pokemon.experience} XP (Level ${pokemon.level})`);
    }

    const oldLevel = pokemon.level || 1;
    const curve = this.getExpCurve(pokemon);
    const oldExp = pokemon.experience;

    // Add experience
    pokemon.experience += expAmount;

    // Calculate new level based on total experience
    const newLevel = getLevelForExp(pokemon.experience, curve);
    pokemon.level = newLevel;

    // Check if Pokemon leveled up
    const didLevelUp = newLevel > oldLevel;

    logger.debug(`XP Update: ${pokemon.name} - Old XP: ${oldExp}, Added: ${expAmount}, New XP: ${pokemon.experience}, Level: ${oldLevel} -> ${newLevel}`);

    if (didLevelUp) {
      logger.debug(`${pokemon.name} leveled up from ${oldLevel} to ${newLevel}!`);
    }

    return {
      pokemon,
      oldLevel,
      newLevel,
      didLevelUp,
      expGained: expAmount
    };
  }

  /**
   * Calculate the initial experience for a Pokemon at a given level
   * @param {number} level - The Pokemon's level
   * @param {string} rarity - The Pokemon's rarity
   * @returns {number} - The initial experience points
   */
  getInitialExpForLevel(level, rarity) {
    if (level <= 1) return 0;

    const curve = getExpCurveForRarity(rarity);
    const exp = getExpForLevel(level, curve);

    // logger.debug(`Initial XP for level ${level} (${rarity}): ${exp}`);
    return exp;
  }
}

// Export a singleton instance
export const experienceSystem = new ExperienceSystem();
