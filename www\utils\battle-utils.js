// utils/battle-utils.js
// Shared utility functions for battle screens

import { logger } from './logger.js';
import { getGermanPokemonName } from './pokemon-display-names.js';
import { getExpForLevel, getExpCurveForRarity } from '../services/experience-system.js';

/**
 * Calculate experience progress for a Pokemon
 * @param {Object} pokemon - The Pokemon object
 * @param {number} additionalExp - Additional experience to show (for animation)
 * @returns {Object} - Experience progress data
 */
export function calculateExpProgress(pokemon, additionalExp = 0) {
  try {
    // Default values
    const result = {
      currentExp: 0,
      currentLevelExp: 0,
      nextLevelExp: 0,
      expInCurrentLevel: 0,
      expNeededForNextLevel: 0,
      progressPercentage: 0,
      newExpPercentage: 0,
      willLevelUp: false
    };

    // Ensure we have a valid Pokemon with level
    if (!pokemon || typeof pokemon.level !== 'number') {
      return result;
    }

    const level = pokemon.level || 1;
    const nextLevel = level + 1;
    const rarity = pokemon.rarity || 'common';
    const curve = getExpCurveForRarity(rarity);

    // Get the base experience for current and next level
    const currentLevelExp = getExpForLevel(level, curve);
    const nextLevelExp = getExpForLevel(nextLevel, curve);

    // Get the current total experience
    const currentExp = typeof pokemon.experience === 'number' ? pokemon.experience : currentLevelExp;

    // Calculate experience in current level and needed for next level
    const expInCurrentLevel = currentExp - currentLevelExp;
    const expNeededForNextLevel = nextLevelExp - currentLevelExp;

    // Calculate progress percentage
    const progressPercentage = Math.min(100, Math.floor((expInCurrentLevel / expNeededForNextLevel) * 100));

    // Calculate new experience with additional exp
    const newExp = currentExp + additionalExp;
    const newExpInLevel = Math.min(newExp - currentLevelExp, expNeededForNextLevel);
    const newProgressPercentage = Math.min(100, Math.floor((newExpInLevel / expNeededForNextLevel) * 100));

    // Check if Pokemon will level up with the additional experience
    const willLevelUp = newExp >= nextLevelExp;

    // Update the result object
    result.currentExp = currentExp;
    result.currentLevelExp = currentLevelExp;
    result.nextLevelExp = nextLevelExp;
    result.expInCurrentLevel = expInCurrentLevel;
    result.expNeededForNextLevel = expNeededForNextLevel;
    result.progressPercentage = progressPercentage;
    result.newExpPercentage = newProgressPercentage;
    result.willLevelUp = willLevelUp;

    return result;
  } catch (e) {
    logger.error('Error in calculateExpProgress:', e);
    return {
      currentExp: 0,
      currentLevelExp: 0,
      nextLevelExp: 0,
      expInCurrentLevel: 0,
      expNeededForNextLevel: 0,
      progressPercentage: 0,
      newExpPercentage: 0,
      willLevelUp: false
    };
  }
}

/**
 * Update type badges for a Pokemon
 * @param {HTMLElement} container - Container element for type badges
 * @param {Object} pokemon - Pokemon data
 */
export function updateTypeBadges(container, pokemon) {
  if (!container || !pokemon) return;

  const types = Array.isArray(pokemon.types) ? pokemon.types : ['Normal'];

  let typeBadgesHtml = '';
  types.forEach(type => {
    typeBadgesHtml += `
      <div class='pokemon-type-badge-group'>
        <span class='pokemon-type-badge' style='background: var(--type-${type.toLowerCase()})'>${type}</span>
      </div>
    `;
  });

  container.innerHTML = typeBadgesHtml;
}

/**
 * Update experience bar for a Pokemon
 * @param {HTMLElement} container - Container element for XP bar
 * @param {Object} pokemon - Pokemon data
 * @param {number} additionalExp - Additional experience to show
 */
export function updateExpBar(container, pokemon, additionalExp = 0) {
  if (!container || !pokemon) return;

  const expProgress = calculateExpProgress(pokemon, additionalExp);

  // Calculate new experience width for animation
  let newExpWidth = 0;
  let levelUpNotification = '';

  if (additionalExp > 0) {
    newExpWidth = expProgress.newExpPercentage - expProgress.progressPercentage;

    // Add level up notification if Pokemon will level up
    if (expProgress.willLevelUp) {
      levelUpNotification = '<div class="level-up-notification">Level up!</div>';
    }
  }

  container.innerHTML = `
    <div class="pokemon-exp-container" style="position: relative;">
      ${levelUpNotification}
      <div class="pokemon-exp-bar">
        <div class="pokemon-exp-fill" style="width: ${expProgress.progressPercentage}%"></div>
        ${newExpWidth > 0 ? `<div class="pokemon-exp-new" style="width: ${newExpWidth}%; right: ${100 - expProgress.progressPercentage - newExpWidth}%;"></div>` : ''}
      </div>
      <div class="pokemon-exp-text">${expProgress.expInCurrentLevel}/${expProgress.expNeededForNextLevel} XP</div>
    </div>
  `;
}

/**
 * Update health bar for a Pokemon
 * @param {HTMLElement} healthFill - Health fill element
 * @param {HTMLElement} healthText - Health text element
 * @param {number} healthPercentage - Health percentage (0-100)
 */
export function updateHealthBar(healthFill, healthText, healthPercentage) {
  if (!healthFill || !healthText) return;

  const clampedHealth = Math.max(0, Math.min(100, healthPercentage));

  healthFill.style.width = `${clampedHealth}%`;
  healthText.textContent = `${Math.round(clampedHealth)}%`;

  // Add low health class if health is below 25%
  if (clampedHealth <= 25) {
    healthFill.classList.add('low-health');
  } else {
    healthFill.classList.remove('low-health');
  }
}

/**
 * Create Pokemon indicators (6 red/gray balls)
 * @param {Array} pokemonStatus - Array of Pokemon status objects
 * @returns {string} - HTML for indicators
 */
export function generatePokemonIndicators(pokemonStatus) {
  let html = '';
  for (let i = 0; i < 6; i++) {
    const isDefeated = pokemonStatus[i] && pokemonStatus[i].defeated;
    const className = isDefeated ? 'pokemon-indicator defeated' : 'pokemon-indicator';
    html += `<div class="${className}"></div>`;
  }
  return html;
}

/**
 * Get display name for Pokemon (German if available)
 * @param {Object} pokemon - Pokemon object
 * @returns {string} - Display name
 */
export function getPokemonDisplayName(pokemon) {
  if (!pokemon) return 'Unknown';
  return getGermanPokemonName(pokemon) || pokemon.name || 'Unknown';
}

/**
 * Get Pokemon image URL
 * @param {Object} pokemon - Pokemon object
 * @returns {string} - Image URL
 */
export function getPokemonImageUrl(pokemon) {
  if (!pokemon) return '';
  return pokemon.image || pokemon.image_url || `./src/PokemonSprites/${pokemon.dex_number}.png`;
}

/**
 * Animate element with CSS class
 * @param {HTMLElement} element - Element to animate
 * @param {string} animationClass - CSS animation class
 * @param {number} duration - Animation duration in ms
 * @returns {Promise<void>}
 */
export function animateElement(element, animationClass, duration = 1000) {
  return new Promise((resolve) => {
    if (!element) {
      resolve();
      return;
    }

    element.classList.add(animationClass);

    setTimeout(() => {
      element.classList.remove(animationClass);
      resolve();
    }, duration);
  });
}

/**
 * Animate the experience bar to show gained experience
 * @param {HTMLElement} container - Container element with XP bar
 * @param {Object} expProgress - Current experience progress data
 * @param {number} expGained - Experience gained
 */
export function animateExpBar(container, expProgress, expGained) {
  try {
    // Find the experience bar elements
    const expFill = container.querySelector('.pokemon-exp-fill');
    const expNew = container.querySelector('.pokemon-exp-new');
    const expText = container.querySelector('.pokemon-exp-text');

    if (!expFill || !expNew || !expText) {
      logger.debug('XP animation elements not found:', { expFill: !!expFill, expNew: !!expNew, expText: !!expText });
      return;
    }

    // Calculate new experience values
    const newExpInLevel = Math.min(
      expProgress.expInCurrentLevel + expGained,
      expProgress.expNeededForNextLevel
    );

    // Calculate the width of the new experience gained
    const newExpWidth = Math.min(100, Math.floor((expGained / expProgress.expNeededForNextLevel) * 100));
    const newPercentage = Math.min(100, Math.floor((newExpInLevel / expProgress.expNeededForNextLevel) * 100));

    logger.debug('XP Animation values:', {
      expGained,
      newExpInLevel,
      newExpWidth,
      newPercentage,
      expProgress
    });

    // Show the new experience bar immediately with semi-transparent effect
    if (expNew && newExpWidth > 0) {
      expNew.style.width = `${newExpWidth}%`;
      expNew.style.right = `${100 - expProgress.progressPercentage - newExpWidth}%`;
      expNew.style.opacity = '0.6'; // Semi-transparent blue
      expNew.style.backgroundColor = '#4a90e2'; // Same blue as main bar
    }

    // Update the text to show new values
    expText.textContent = `${newExpInLevel}/${expProgress.expNeededForNextLevel} XP`;

    // After a short delay, animate the main fill to include the new experience
    setTimeout(() => {
      // Update the position of the new exp bar as the fill grows
      if (expNew) {
        expNew.style.right = `${100 - newPercentage}%`;
      }

      // Animate the fill to the new width
      expFill.style.width = `${newPercentage}%`;

      // Fade out the new experience part as the main fill takes over
      setTimeout(() => {
        if (expNew) {
          expNew.style.opacity = '0';
        }
      }, 300);
    }, 500);
  } catch (e) {
    logger.error('Error animating experience bar:', e);
  }
}

/**
 * Show level up notification with animation
 * @param {HTMLElement} container - Container element
 * @param {Object} pokemon - Pokemon that leveled up
 * @param {number} oldLevel - Previous level
 * @param {number} newLevel - New level
 * @returns {Promise<void>}
 */
export function showLevelUpNotification(container, pokemon, oldLevel, newLevel) {
  return new Promise((resolve) => {
    if (!container) {
      resolve();
      return;
    }

    const message = `${getPokemonDisplayName(pokemon)} erreicht Level ${newLevel}!`;
    showNotification(container, message, 'level-up-notification', 3000).then(resolve);

    logger.debug(`${pokemon.name} leveled up from ${oldLevel} to ${newLevel}!`);
  });
}

/**
 * Show temporary notification
 * @param {HTMLElement} container - Container for notification
 * @param {string} message - Notification message
 * @param {string} className - CSS class for styling
 * @param {number} duration - Duration in ms
 * @returns {Promise<void>}
 */
export function showNotification(container, message, className = 'notification', duration = 3000) {
  return new Promise((resolve) => {
    if (!container) {
      resolve();
      return;
    }

    const notification = document.createElement('div');
    notification.className = className;
    notification.textContent = message;
    notification.style.opacity = '1';

    container.appendChild(notification);

    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
      resolve();
    }, duration);
  });
}
