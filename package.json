{"name": "gps-pokemon-app", "version": "1.0.0", "description": "GPS Pokémon Spiel mit Capacitor, Leaflet und OSM", "scripts": {"start": "npx live-server", "cap-android": "npx cap sync android", "ionic:serve": "npx http-server ./www -p 8100 -o", "ionic:build": "echo 'Build completed'"}, "dependencies": {"@capacitor-community/keep-awake": "^7.0.0", "@capacitor/android": "^7.2.0", "@capacitor/app": "^7.0.1", "@capacitor/cli": "^7.2.0", "@capacitor/core": "^7.2.0", "@capacitor/geolocation": "^7.1.1", "@capacitor/preferences": "^7.0.2", "@turf/turf": "^7.2.0", "node-fetch": "^2.7.0"}, "devDependencies": {"http-server": "^14.1.1", "serve": "^14.2.4"}}