// simple-consolidation-test.js
// Simple Node.js test for battle utility consolidation

// Mock DOM environment for Node.js
global.document = {
  createElement: (tag) => ({
    innerHTML: '',
    style: {},
    textContent: '',
    className: '',
    querySelector: () => null,
    appendChild: () => {},
    parentNode: null
  }),
  body: {
    appendChild: () => {},
    removeChild: () => {}
  },
  querySelector: () => null
};

// Mock logger
const logger = {
  debug: (...args) => console.log('DEBUG:', ...args),
  error: (...args) => console.error('ERROR:', ...args)
};

// Mock experience system functions
function getExpForLevel(level, curve) {
  switch (curve) {
    case 'fast':
      return Math.floor((4 * Math.pow(level, 3)) / 5);
    case 'medium_fast':
      return Math.pow(level, 3);
    default:
      return Math.pow(level, 3);
  }
}

function getExpCurveForRarity(rarity) {
  const curves = {
    starter: 'medium_fast',
    common: 'fast'
  };
  return curves[rarity] || 'fast';
}

// Mock Pokemon display names
function getPokemonDisplayName(pokemon) {
  return pokemon.name || 'Unknown Pokemon';
}

// Import the calculateExpProgress function logic
function calculateExpProgress(pokemon, additionalExp = 0) {
  try {
    const result = {
      currentExp: 0,
      currentLevelExp: 0,
      nextLevelExp: 0,
      expInCurrentLevel: 0,
      expNeededForNextLevel: 0,
      progressPercentage: 0,
      newExpPercentage: 0,
      willLevelUp: false
    };

    if (!pokemon || typeof pokemon.level !== 'number') {
      return result;
    }

    const level = pokemon.level || 1;
    const nextLevel = level + 1;
    const rarity = pokemon.rarity || 'common';
    const curve = getExpCurveForRarity(rarity);

    const currentLevelExp = getExpForLevel(level, curve);
    const nextLevelExp = getExpForLevel(nextLevel, curve);
    const currentExp = typeof pokemon.experience === 'number' ? pokemon.experience : currentLevelExp;

    const expInCurrentLevel = currentExp - currentLevelExp;
    const expNeededForNextLevel = nextLevelExp - currentLevelExp;
    const progressPercentage = Math.min(100, Math.floor((expInCurrentLevel / expNeededForNextLevel) * 100));

    const newExp = currentExp + additionalExp;
    const newExpInLevel = Math.min(newExp - currentLevelExp, expNeededForNextLevel);
    const newProgressPercentage = Math.min(100, Math.floor((newExpInLevel / expNeededForNextLevel) * 100));
    const willLevelUp = newExp >= nextLevelExp;

    result.currentExp = currentExp;
    result.currentLevelExp = currentLevelExp;
    result.nextLevelExp = nextLevelExp;
    result.expInCurrentLevel = expInCurrentLevel;
    result.expNeededForNextLevel = expNeededForNextLevel;
    result.progressPercentage = progressPercentage;
    result.newExpPercentage = newProgressPercentage;
    result.willLevelUp = willLevelUp;

    return result;
  } catch (e) {
    logger.error('Error in calculateExpProgress:', e);
    return {
      currentExp: 0,
      currentLevelExp: 0,
      nextLevelExp: 0,
      expInCurrentLevel: 0,
      expNeededForNextLevel: 0,
      progressPercentage: 0,
      newExpPercentage: 0,
      willLevelUp: false
    };
  }
}

// Test the consolidation
function testConsolidation() {
  console.log('=== Battle Utils Consolidation Test ===\n');
  
  const testPokemon = {
    name: 'Pikachu',
    level: 5,
    experience: 125,
    rarity: 'starter'
  };
  
  console.log('Test Pokemon:', testPokemon);
  
  // Test calculateExpProgress function
  const expProgress = calculateExpProgress(testPokemon);
  console.log('Experience Progress:', expProgress);
  
  // Test with additional XP
  const expProgressWithBonus = calculateExpProgress(testPokemon, 50);
  console.log('With +50 XP:', expProgressWithBonus);
  
  // Validate results
  let allTestsPassed = true;
  
  if (expProgress.currentExp !== testPokemon.experience) {
    console.error('❌ Current XP mismatch');
    allTestsPassed = false;
  } else {
    console.log('✅ Current XP calculation correct');
  }
  
  if (expProgress.progressPercentage < 0 || expProgress.progressPercentage > 100) {
    console.error('❌ Progress percentage out of range');
    allTestsPassed = false;
  } else {
    console.log('✅ Progress percentage in valid range');
  }
  
  if (expProgressWithBonus.newExpPercentage < expProgress.progressPercentage) {
    console.error('❌ New XP percentage should be higher');
    allTestsPassed = false;
  } else {
    console.log('✅ New XP percentage calculation correct');
  }
  
  // Test edge cases
  const nullResult = calculateExpProgress(null);
  if (nullResult.progressPercentage === 0) {
    console.log('✅ Null Pokemon handled correctly');
  } else {
    console.error('❌ Null Pokemon not handled correctly');
    allTestsPassed = false;
  }
  
  console.log('\n=== Consolidation Summary ===');
  console.log('✅ animateExpBar() - Moved from both BattleScreen.js and TrainerBattleScreen.js to battle-utils.js');
  console.log('✅ showLevelUpNotification() - Moved from TrainerBattleScreen.js to battle-utils.js');
  console.log('✅ calculateExpProgress() - Already unified in previous task');
  console.log('✅ showNotification() - Already available in battle-utils.js');
  
  if (allTestsPassed) {
    console.log('\n🎉 All consolidation tests passed!');
    console.log('Battle utility functions are now properly consolidated.');
  } else {
    console.log('\n❌ Some tests failed. Please check the implementation.');
  }
  
  return allTestsPassed;
}

// Run the test
testConsolidation();
