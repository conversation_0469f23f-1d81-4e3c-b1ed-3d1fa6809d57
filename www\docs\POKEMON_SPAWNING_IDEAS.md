Das Hauptproblem liegt in den redundanten OSM/Overpass-API-Aufrufen. Hier sind die wichtigsten Optimierungsansätze:
Hauptprobleme im aktuellen System:

    40 individuelle Landuse-API-Calls: <PERSON><PERSON><PERSON> jedes der 40 Pokémon wird getLanduseForLatLng() aufgerufen

    Doppelte Arbeit: Landuse-Daten werden sowohl für zufällige als auch spezielle Pokémon abgerufen

    Sequenzielle Verarbeitung: Trotz parallel data collection gibt es Bottlenecks

Optimierte Lösung:
1. Landuse-Daten einmal laden und cachen

/**
 * Load all landuse data once and cache it for the spawn session
 */
async loadLanduseDataForArea(lat, lng, radius) {
  const cacheKey = `${Math.round(lat * 1000)}:${Math.round(lng * 1000)}:${radius}`;
  
  if (this.landuseCache && this.landuseCache.key === cacheKey) {
    return this.landuseCache.data;
  }

  // Load all landuse polygons once
  const geojson = await getLandusePolygonsGeoJSON(lat, lng, radius);
  
  this.landuseCache = {
    key: cacheKey,
    data: geojson,
    timestamp: Date.now()
  };
  
  return geojson;
}

/**
 * Fast landuse lookup using cached data
 */
getLanduseForPoint(lat, lng) {
  if (!this.landuseCache?.data?.features) return null;
  
  const point = turf.point([lng, lat]);
  
  // Find the polygon containing this point
  for (const feature of this.landuseCache.data.features) {
    if (turf.booleanPointInPolygon(point, feature)) {
      return {
        value: feature.properties.value,
        feature: feature
      };
    }
  }
  
  return null;
}

2. Unified Spawning-Methode

/**
 * Spawn all Pokemon in one optimized batch
 */
async spawnAllPokemons(lat, lng, randomCount = 20, landuseCount = 20) {
  const startTime = Date.now();
  
  // 1. Load landuse data once for the entire area
  const landuseData = await this.loadLanduseDataForArea(lat, lng, this.spawnRadius);
  
  // 2. Create pokedex snapshot once
  const pokedexSnapshot = JSON.parse(JSON.stringify(gameState.pokedexData));
  
  // 3. Calculate movement direction once
  const movementData = this.calculateMovementData(lat, lng);
  
  // 4. Generate all spawn locations at once
  const spawnLocations = this.generateAllSpawnLocations(
    lat, lng, randomCount + landuseCount, movementData
  );
  
  // 5. Parallel data collection for ALL pokemon
  const allPokemonDataPromises = spawnLocations.map((location, index) => {
    const isLanduseSpecial = index < landuseCount;
    return this.collectPokemonDataOptimized(
      location, isLanduseSpecial, pokedexSnapshot
    );
  });
  
  // 6. Wait for all data collection
  const allPokemonData = await Promise.all(allPokemonDataPromises);
  
  // 7. Create final Pokemon objects
  const finalizedPokemon = [];
  for (const pokemonData of allPokemonData) {
    if (pokemonData) {
      const finalPokemon = pokemonData.isLanduseSpecial 
        ? this.createFinalLandusePokemon(pokemonData)
        : this.createFinalPokemon(pokemonData);
      
      if (finalPokemon) {
        finalizedPokemon.push(finalPokemon);
        gameState.addPokemon(finalPokemon);
      }
    }
  }
  
  const duration = Date.now() - startTime;
  logger.info(`Spawned ${finalizedPokemon.length} Pokemon in ${duration}ms`);
  
  return finalizedPokemon;
}

3. Optimierte Datensammlung

/**
 * Optimized data collection using cached landuse data
 */
async collectPokemonDataOptimized(spawnLocation, isLanduseSpecial, pokedexSnapshot) {
  try {
    // Fast landuse lookup using cache
    const landuseData = this.getLanduseForPoint(spawnLocation.lat, spawnLocation.lng);
    
    // Get base Pokemon for grid
    const basePokeEntry = isLanduseSpecial 
      ? this.getBasePokeemonForLanduse(landuseData, pokedexSnapshot)
      : this.getBasePokemonForGrid(spawnLocation.lat, spawnLocation.lng);
    
    if (!basePokeEntry) return null;
    
    // Get spawn level (can be cached if based on player team)
    const level = await this.getSpawnLevel();
    
    // Calculate evolution data
    const evolutionData = await this.calculateEvolutionData(
      basePokeEntry.name, level, pokedexSnapshot
    );
    
    return {
      basePokeEntry,
      level,
      evolutionData,
      spawnLocation,
      landuseData,
      isLanduseSpecial,
      timestamp: Date.now()
    };
  } catch (e) {
    logger.error('Error collecting optimized Pokemon data:', e);
    return null;
  }
}

4. Streaming-Ansatz

/**
 * Stream Pokemon spawning for better perceived performance
 */
async spawnPokemonsStreaming(lat, lng, totalCount = 40) {
  const batchSize = 8; // Show 8 Pokemon at a time
  const batches = Math.ceil(totalCount / batchSize);
  
  // Load landuse data once
  await this.loadLanduseDataForArea(lat, lng, this.spawnRadius);
  
  const allSpawned = [];
  
  for (let i = 0; i < batches; i++) {
    const batchStart = i * batchSize;
    const batchEnd = Math.min((i + 1) * batchSize, totalCount);
    const batchCount = batchEnd - batchStart;
    
    // Spawn this batch
    const batchPokemons = await this.spawnBatchOptimized(
      lat, lng, batchCount, batchStart
    );
    
    allSpawned.push(...batchPokemons);
    
    // Show immediately on map
    this.displayPokemonBatch(batchPokemons);
    
    // Small delay to prevent UI blocking
    if (i < batches - 1) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }
  
  return allSpawned;
}

## Fazit
Die wichtigste Erkenntnis: Ein API-Call für alle Landuse-Daten statt 40 einzelne Calls ist der Schlüssel zur Performance-Verbesserung!