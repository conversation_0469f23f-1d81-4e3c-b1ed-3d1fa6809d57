Logger initialized in browser environment logger.js:26:15
[unknown:0] StorageService initialized 
Object { capacitorAvailable: false }
logger.js:112:17
[unknown:0] GameState initialized logger.js:112:17
[unknown:0] Initializing app... logger.js:138:17
[unknown:0] Loaded pokedex from storage - count: 0 logger.js:138:17
[unknown:0] Type effectiveness data loaded successfully logger.js:112:17
[unknown:0] Pokedex data loaded - pokemonCount: 151, chainsCount: 78 logger.js:138:17
[unknown:0] GameState initialized successfully logger.js:138:17
[unknown:0] Loaded 0 Pokemon from storage logger.js:112:17
[unknown:0] Loaded 0 Pokemon in team logger.js:112:17
[unknown:0] Pokemon manager initialized logger.js:138:17
GET
http://127.0.0.1:8000/favicon.ico
[HTTP/1.1 404 Not Found 1ms]

Sprite base path: ./src/PlayerSprites/male/ player-renderer.js:45:13
Player size: 16 x 24 player-renderer.js:72:13
Using icon size: 
Array [ 16, 24 ]
player-renderer.js:76:13
Player sprite created at 51.96 7.62 player-renderer.js:99:13
[unknown:0] Player sprite initialized at map center: 51.96,7.62 logger.js:112:17
[unknown:0] Initializing time-event system... logger.js:138:17
[unknown:0] Loaded 20 Pokemon spawns from storage logger.js:112:17
[unknown:0] Loading 20 Pokemon spawns from storage logger.js:138:17
[unknown:0] Recreating 20 Pokemon from storage logger.js:138:17
[unknown:0] Creating new Pokemon geodude with dex_number: 74 (from evolutionData.dex: 74) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized geodude (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] Setting dex_number for geodude (ID: mdptgqr4ejt56kd): 74 logger.js:112:17
[unknown:0] Loaded geodude with 100 XP (Level 5) from _experience logger.js:112:17
[unknown:0] Recreated Pokemon geodude (ID: mdptgqr4ejt56kd) with dex_number: 74 logger.js:112:17
[unknown:0] Creating new Pokemon bellsprout with dex_number: 69 (from evolutionData.dex: 69) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized bellsprout (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] Setting dex_number for bellsprout (ID: mdptgqr4vfix62h): 69 logger.js:112:17
[unknown:0] Loaded bellsprout with 100 XP (Level 5) from _experience logger.js:112:17
[unknown:0] Recreated Pokemon bellsprout (ID: mdptgqr4vfix62h) with dex_number: 69 logger.js:112:17
[unknown:0] Creating new Pokemon meowth with dex_number: 52 (from evolutionData.dex: 52) logger.js:112:17
[unknown:0] Setting dex_number for meowth (ID: mdptgqr4w1d5yri): 52 logger.js:112:17
[unknown:0] Loaded meowth with 0 XP (Level 1) from _experience logger.js:112:17
[unknown:0] Recreated Pokemon meowth (ID: mdptgqr4w1d5yri) with dex_number: 52 logger.js:112:17
[unknown:0] Creating new Pokemon mankey with dex_number: 56 (from evolutionData.dex: 56) logger.js:112:17
[unknown:0] Initial XP for level 10 (common): 800 logger.js:112:17
[unknown:0] Initialized mankey (Lvl 10) with 800 XP logger.js:112:17
[unknown:0] Setting dex_number for mankey (ID: mdptgqr4thhmtnj): 56 logger.js:112:17
[unknown:0] Loaded mankey with 800 XP (Level 10) from _experience logger.js:112:17
[unknown:0] Recreated Pokemon mankey (ID: mdptgqr4thhmtnj) with dex_number: 56 logger.js:112:17
[unknown:0] Creating new Pokemon seel with dex_number: 86 (from evolutionData.dex: 86) logger.js:112:17
[unknown:0] Initial XP for level 6 (common): 172 logger.js:112:17
[unknown:0] Initialized seel (Lvl 6) with 172 XP logger.js:112:17
[unknown:0] Setting dex_number for seel (ID: mdptgqr5q1o8rd8): 86 logger.js:112:17
[unknown:0] Loaded seel with 172 XP (Level 6) from _experience logger.js:112:17
[unknown:0] Recreated Pokemon seel (ID: mdptgqr5q1o8rd8) with dex_number: 86 logger.js:112:17
[unknown:0] Creating new Pokemon cubone with dex_number: 104 (from evolutionData.dex: 104) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized cubone (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] Setting dex_number for cubone (ID: mdptgqr5u80usye): 104 logger.js:112:17
[unknown:0] Loaded cubone with 100 XP (Level 5) from _experience logger.js:112:17
[unknown:0] Recreated Pokemon cubone (ID: mdptgqr5u80usye) with dex_number: 104 logger.js:112:17
[unknown:0] Creating new Pokemon mew with dex_number: 151 (from evolutionData.dex: 151) logger.js:112:17
[unknown:0] Initial XP for level 3 (mythical): 33 logger.js:112:17
[unknown:0] Initialized mew (Lvl 3) with 33 XP logger.js:112:17
[unknown:0] Setting dex_number for mew (ID: mdptgqr6r53sl40): 151 logger.js:112:17
[unknown:0] Loaded mew with 21 XP (Level 3) from _experience logger.js:112:17
[unknown:0] Recreated Pokemon mew (ID: mdptgqr6r53sl40) with dex_number: 151 logger.js:112:17
[unknown:0] Creating new Pokemon bellsprout with dex_number: 69 (from evolutionData.dex: 69) logger.js:112:17
[unknown:0] Initial XP for level 2 (common): 6 logger.js:112:17
[unknown:0] Initialized bellsprout (Lvl 2) with 6 XP logger.js:112:17
[unknown:0] Setting dex_number for bellsprout (ID: mdptgqr6uwmro3j): 69 logger.js:112:17
[unknown:0] Loaded bellsprout with 6 XP (Level 2) from _experience logger.js:112:17
[unknown:0] Recreated Pokemon bellsprout (ID: mdptgqr6uwmro3j) with dex_number: 69 logger.js:112:17
[unknown:0] Creating new Pokemon seel with dex_number: 86 (from evolutionData.dex: 86) logger.js:112:17
[unknown:0] Initial XP for level 6 (common): 172 logger.js:112:17
[unknown:0] Initialized seel (Lvl 6) with 172 XP logger.js:112:17
[unknown:0] Setting dex_number for seel (ID: mdptgqr6qhj691f): 86 logger.js:112:17
[unknown:0] Loaded seel with 172 XP (Level 6) from _experience logger.js:112:17
[unknown:0] Recreated Pokemon seel (ID: mdptgqr6qhj691f) with dex_number: 86 logger.js:112:17
[unknown:0] Creating new Pokemon geodude with dex_number: 74 (from evolutionData.dex: 74) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized geodude (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] Setting dex_number for geodude (ID: mdptgqr6c16dz10): 74 logger.js:112:17
[unknown:0] Loaded geodude with 100 XP (Level 5) from _experience logger.js:112:17
[unknown:0] Recreated Pokemon geodude (ID: mdptgqr6c16dz10) with dex_number: 74 logger.js:112:17
[unknown:0] Creating new Pokemon caterpie with dex_number: 12 (from evolutionData.dex: 12) logger.js:112:17
[unknown:0] Initial XP for level 18 (common): 4665 logger.js:112:17
[unknown:0] Initialized caterpie (Lvl 18) with 4665 XP logger.js:112:17
[unknown:0] Setting dex_number for butterfree (ID: mdptgqr6typybm3): 12 logger.js:112:17
[unknown:0] Loaded butterfree with 4665 XP (Level 18) from _experience logger.js:112:17
[unknown:0] Recreated Pokemon butterfree (ID: mdptgqr6typybm3) with dex_number: 12 logger.js:112:17
[unknown:0] Creating new Pokemon bellsprout with dex_number: 69 (from evolutionData.dex: 69) logger.js:112:17
[unknown:0] Setting dex_number for bellsprout (ID: mdptgqr7p34zy5b): 69 logger.js:112:17
[unknown:0] Loaded bellsprout with 0 XP (Level 1) from _experience logger.js:112:17
[unknown:0] Recreated Pokemon bellsprout (ID: mdptgqr7p34zy5b) with dex_number: 69 logger.js:112:17
[unknown:0] Creating new Pokemon geodude with dex_number: 74 (from evolutionData.dex: 74) logger.js:112:17
[unknown:0] Setting dex_number for geodude (ID: mdptgqr7zfp1em0): 74 logger.js:112:17
[unknown:0] Loaded geodude with 0 XP (Level 1) from _experience logger.js:112:17
[unknown:0] Recreated Pokemon geodude (ID: mdptgqr7zfp1em0) with dex_number: 74 logger.js:112:17
[unknown:0] Creating new Pokemon geodude with dex_number: 74 (from evolutionData.dex: 74) logger.js:112:17
[unknown:0] Initial XP for level 6 (common): 172 logger.js:112:17
[unknown:0] Initialized geodude (Lvl 6) with 172 XP logger.js:112:17
[unknown:0] Setting dex_number for geodude (ID: mdptgqr7b42e2ae): 74 logger.js:112:17
[unknown:0] Loaded geodude with 172 XP (Level 6) from _experience logger.js:112:17
[unknown:0] Recreated Pokemon geodude (ID: mdptgqr7b42e2ae) with dex_number: 74 logger.js:112:17
[unknown:0] Creating new Pokemon seel with dex_number: 86 (from evolutionData.dex: 86) logger.js:112:17
[unknown:0] Initial XP for level 3 (common): 21 logger.js:112:17
[unknown:0] Initialized seel (Lvl 3) with 21 XP logger.js:112:17
[unknown:0] Setting dex_number for seel (ID: mdptgqr7a5297lx): 86 logger.js:112:17
[unknown:0] Loaded seel with 21 XP (Level 3) from _experience logger.js:112:17
[unknown:0] Recreated Pokemon seel (ID: mdptgqr7a5297lx) with dex_number: 86 logger.js:112:17
[unknown:0] Creating new Pokemon mew with dex_number: 151 (from evolutionData.dex: 151) logger.js:112:17
[unknown:0] Initial XP for level 4 (mythical): 80 logger.js:112:17
[unknown:0] Initialized mew (Lvl 4) with 80 XP logger.js:112:17
[unknown:0] Setting dex_number for mew (ID: mdptgqr832v13sp): 151 logger.js:112:17
[unknown:0] Loaded mew with 51 XP (Level 4) from _experience logger.js:112:17
[unknown:0] Recreated Pokemon mew (ID: mdptgqr832v13sp) with dex_number: 151 logger.js:112:17
[unknown:0] Creating new Pokemon seel with dex_number: 86 (from evolutionData.dex: 86) logger.js:112:17
[unknown:0] Initial XP for level 22 (common): 8518 logger.js:112:17
[unknown:0] Initialized seel (Lvl 22) with 8518 XP logger.js:112:17
[unknown:0] Setting dex_number for seel (ID: mdptgqr8q2qkfls): 86 logger.js:112:17
[unknown:0] Loaded seel with 8518 XP (Level 22) from _experience logger.js:112:17
[unknown:0] Recreated Pokemon seel (ID: mdptgqr8q2qkfls) with dex_number: 86 logger.js:112:17
[unknown:0] Creating new Pokemon meowth with dex_number: 52 (from evolutionData.dex: 52) logger.js:112:17
[unknown:0] Setting dex_number for meowth (ID: mdptgqr8ch26xng): 52 logger.js:112:17
[unknown:0] Loaded meowth with 0 XP (Level 1) from _experience logger.js:112:17
[unknown:0] Recreated Pokemon meowth (ID: mdptgqr8ch26xng) with dex_number: 52 logger.js:112:17
[unknown:0] Creating new Pokemon geodude with dex_number: 74 (from evolutionData.dex: 74) logger.js:112:17
[unknown:0] Setting dex_number for geodude (ID: mdptgqr82pi37f5): 74 logger.js:112:17
[unknown:0] Loaded geodude with 0 XP (Level 1) from _experience logger.js:112:17
[unknown:0] Recreated Pokemon geodude (ID: mdptgqr82pi37f5) with dex_number: 74 logger.js:112:17
[unknown:0] Creating new Pokemon cubone with dex_number: 104 (from evolutionData.dex: 104) logger.js:112:17
[unknown:0] Initial XP for level 7 (common): 274 logger.js:112:17
[unknown:0] Initialized cubone (Lvl 7) with 274 XP logger.js:112:17
[unknown:0] Setting dex_number for cubone (ID: mdptgqr9vcfyu3r): 104 logger.js:112:17
[unknown:0] Loaded cubone with 274 XP (Level 7) from _experience logger.js:112:17
[unknown:0] Recreated Pokemon cubone (ID: mdptgqr9vcfyu3r) with dex_number: 104 logger.js:112:17
[unknown:0] Successfully recreated 20 Pokemon from storage logger.js:138:17
[unknown:0] Hour change timer is disabled - time slots only change on app restart logger.js:112:17
[unknown:0] Created new FAB bar with grid layout logger.js:112:17
[unknown:0] Created FAB button with ID submenu-fab at grid position 4 logger.js:112:17
[unknown:0] Created FAB button with ID battle-fab at grid position 1 logger.js:112:17
[unknown:0] Created FAB button with ID debug-fab at grid position 3 logger.js:112:17
[unknown:0] Created FAB button with ID center-fab at grid position 2 logger.js:112:17
[unknown:0] FabSubmenuManager initialized logger.js:112:17
[unknown:0] FabManager initialized logger.js:112:17
[unknown:0] Browser geolocation available - permissions will be requested when accessing location logger.js:138:17
[unknown:0] Capacitor KeepAwake Plugin not available logger.js:190:17
[unknown:0] Failed to keep screen awake. The screen may turn off after a period of inactivity. logger.js:164:17
[unknown:0] Using Browser Geolocation API for position watching logger.js:138:17
[unknown:0] Started Browser position watch with ID: 1 logger.js:138:17
Der Orientierungssensor sollte nicht mehr verwendet werden. main.js:630:12
[unknown:0] App initialized successfully logger.js:138:17
[unknown:0] Player is 67.28682533598638m from stored Pokemon (< 500m) - keeping stored spawns logger.js:138:17
[unknown:0] Set lastSpawnLatLng to 50.49160743474863, 10.856102128127468 based on average position of stored Pokemon logger.js:112:17
[unknown:0] Browser Geolocation update: lat=50.4922112, lng=10.8560384, accuracy=1219.9206785921297 logger.js:112:17
[unknown:0] Spawning test trainer... logger.js:112:17
[unknown:0] Loaded 34 trainer types logger.js:112:17
[unknown:0] Loaded 100 male and 100 female NPC names logger.js:112:17
[unknown:0] Creating new Pokemon magnemite with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 42 (common): 59270 logger.js:112:17
[unknown:0] Initialized magnemite (Lvl 42) with 59270 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: magnemite logger.js:112:17
[unknown:0] Pokemon family for magnemite (chain 34): magnemite (Dex #81, Evo level: 30), magneton (Dex #82, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdpthezutehhwxc, base_name=magnemite, name=magnemite, level=42 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: magnemite, dex: 81, evolution_level: 30}, {name: magneton, dex: 82, evolution_level: none} logger.js:112:17
[unknown:0] Evolution by level: magnemite -> magneton (Level: 42 >= 30) logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=magneton, dex=82, evolution_level=none logger.js:112:17
[unknown:0] Creating new Pokemon magnemite with dex_number: 81 (from evolutionData.dex: 81) logger.js:112:17
[unknown:0] Initial XP for level 42 (common): 59270 logger.js:112:17
[unknown:0] Initialized magnemite (Lvl 42) with 59270 XP logger.js:112:17
[unknown:0] Created trainer Pokemon: magneton (Level 42) evolved from magnemite logger.js:112:17
[unknown:0] Creating new Pokemon pikachu with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 31 (common): 23832 logger.js:112:17
[unknown:0] Initialized pikachu (Lvl 31) with 23832 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: pikachu logger.js:112:17
[unknown:0] Pokemon family for pikachu (chain 10): pikachu (Dex #25, Evo level: 32), raichu (Dex #26, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdpthezvyzv0yqo, base_name=pikachu, name=pikachu, level=31 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: pikachu, dex: 25, evolution_level: 32}, {name: raichu, dex: 26, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=pikachu, dex=25, evolution_level=32 logger.js:112:17
[unknown:0] Creating new Pokemon pikachu with dex_number: 25 (from evolutionData.dex: 25) logger.js:112:17
[unknown:0] Initial XP for level 31 (common): 23832 logger.js:112:17
[unknown:0] Initialized pikachu (Lvl 31) with 23832 XP logger.js:112:17
[unknown:0] Created trainer Pokemon: pikachu (Level 31) evolved from pikachu logger.js:112:17
[unknown:0] Creating new Pokemon voltorb with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 33 (common): 28749 logger.js:112:17
[unknown:0] Initialized voltorb (Lvl 33) with 28749 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: voltorb logger.js:112:17
[unknown:0] Pokemon family for voltorb (chain 44): voltorb (Dex #100, Evo level: 30), electrode (Dex #101, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdpthezwpcvepv6, base_name=voltorb, name=voltorb, level=33 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: voltorb, dex: 100, evolution_level: 30}, {name: electrode, dex: 101, evolution_level: none} logger.js:112:17
[unknown:0] Evolution by level: voltorb -> electrode (Level: 33 >= 30) logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=electrode, dex=101, evolution_level=none logger.js:112:17
[unknown:0] Creating new Pokemon voltorb with dex_number: 100 (from evolutionData.dex: 100) logger.js:112:17
[unknown:0] Initial XP for level 33 (common): 28749 logger.js:112:17
[unknown:0] Initialized voltorb (Lvl 33) with 28749 XP logger.js:112:17
[unknown:0] Created trainer Pokemon: electrode (Level 33) evolved from voltorb logger.js:112:17
[unknown:0] Creating new Pokemon magneton with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 38 (common): 43897 logger.js:112:17
[unknown:0] Initialized magneton (Lvl 38) with 43897 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: magneton logger.js:112:17
[unknown:0] Pokemon family for magneton (chain 34): magnemite (Dex #81, Evo level: 30), magneton (Dex #82, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdpthezx7wz5k4c, base_name=magneton, name=magneton, level=38 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: magnemite, dex: 81, evolution_level: 30}, {name: magneton, dex: 82, evolution_level: none} logger.js:112:17
[unknown:0] Evolution by level: magnemite -> magneton (Level: 38 >= 30) logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=magneton, dex=82, evolution_level=none logger.js:112:17
[unknown:0] Creating new Pokemon magneton with dex_number: 82 (from evolutionData.dex: 82) logger.js:112:17
[unknown:0] Initial XP for level 38 (common): 43897 logger.js:112:17
[unknown:0] Initialized magneton (Lvl 38) with 43897 XP logger.js:112:17
[unknown:0] Created trainer Pokemon: magneton (Level 38) evolved from magneton logger.js:112:17
[unknown:0] Creating new Pokemon magnemite with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 28 (common): 17561 logger.js:112:17
[unknown:0] Initialized magnemite (Lvl 28) with 17561 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: magnemite logger.js:112:17
[unknown:0] Pokemon family for magnemite (chain 34): magnemite (Dex #81, Evo level: 30), magneton (Dex #82, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdpthezy63kjlno, base_name=magnemite, name=magnemite, level=28 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: magnemite, dex: 81, evolution_level: 30}, {name: magneton, dex: 82, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=magnemite, dex=81, evolution_level=30 logger.js:112:17
[unknown:0] Creating new Pokemon magnemite with dex_number: 81 (from evolutionData.dex: 81) logger.js:112:17
[unknown:0] Initial XP for level 28 (common): 17561 logger.js:112:17
[unknown:0] Initialized magnemite (Lvl 28) with 17561 XP logger.js:112:17
[unknown:0] Created trainer Pokemon: magnemite (Level 28) evolved from magnemite logger.js:112:17
[unknown:0] Creating new Pokemon pikachu with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 31 (common): 23832 logger.js:112:17
[unknown:0] Initialized pikachu (Lvl 31) with 23832 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: pikachu logger.js:112:17
[unknown:0] Pokemon family for pikachu (chain 10): pikachu (Dex #25, Evo level: 32), raichu (Dex #26, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdpthezygxf1ukx, base_name=pikachu, name=pikachu, level=31 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: pikachu, dex: 25, evolution_level: 32}, {name: raichu, dex: 26, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=pikachu, dex=25, evolution_level=32 logger.js:112:17
[unknown:0] Creating new Pokemon pikachu with dex_number: 25 (from evolutionData.dex: 25) logger.js:112:17
[unknown:0] Initial XP for level 31 (common): 23832 logger.js:112:17
[unknown:0] Initialized pikachu (Lvl 31) with 23832 XP logger.js:112:17
[unknown:0] Created trainer Pokemon: pikachu (Level 31) evolved from pikachu logger.js:112:17
[unknown:0] Generated team of 6 Pokemon for trainer logger.js:112:17
[unknown:0] Created trainer Antonin (Guitarist) with 6 Pokemon, average level 34 logger.js:112:17
[unknown:0] Set trainer Antonin position to 50.49192061835428, 10.856213516410756 logger.js:112:17
[unknown:0] Spawned trainer: Antonin (Guitarist) at 50.49192061835428, 10.856213516410756 logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] Updating player sprite at 50.4922112, 10.8560384, heading: 0 logger.js:112:17
[unknown:0] Player is static, distSinceLastMove: 0 logger.js:112:17
[unknown:0] Browser Geolocation update: lat=50.4922112, lng=10.8560384, accuracy=1219.9206785921297 logger.js:112:17
[unknown:0] Updating player sprite at 50.4922112, 10.8560384, heading: 0 logger.js:112:17
[unknown:0] Player is static, distSinceLastMove: 0 logger.js:112:17
[unknown:0] Browser Geolocation update: lat=50.4922112, lng=10.8560384, accuracy=1219.9206785921297 logger.js:112:17
[unknown:0] Updating player sprite at 50.4922112, 10.8560384, heading: 0 logger.js:112:17
[unknown:0] Player is static, distSinceLastMove: 0 logger.js:112:17
[unknown:0] Grid cell (2553,18680) maps to chain index 1 of 78 (rarity: starter) logger.js:112:17
[unknown:0] Grid cell (2554,18680) maps to chain index 37 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2555,18680) maps to chain index 77 of 78 (rarity: mythical) logger.js:112:17
[unknown:0] Grid cell (2556,18680) maps to chain index 40 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2557,18680) maps to chain index 56 of 78 (rarity: rare) logger.js:112:17
[unknown:0] Grid cell (2553,18681) maps to chain index 7 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2554,18681) maps to chain index 36 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2555,18681) maps to chain index 3 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2556,18681) maps to chain index 45 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2557,18681) maps to chain index 65 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2553,18682) maps to chain index 12 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2554,18682) maps to chain index 28 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2555,18682) maps to chain index 30 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2556,18682) maps to chain index 21 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2557,18682) maps to chain index 23 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2553,18683) maps to chain index 36 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2554,18683) maps to chain index 28 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2555,18683) maps to chain index 22 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2556,18683) maps to chain index 35 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2557,18683) maps to chain index 10 of 78 (rarity: common) logger.js:112:17
[unknown:0] Debug mode toggled: true logger.js:112:17
[unknown:0] Starting spawn reset process... logger.js:112:17
[unknown:0] Clearing Pokemon spawns from storage logger.js:112:17
[unknown:0] Removed from storage: timeEventSpawns logger.js:112:17
[unknown:0] Cleared spawns from storage: success logger.js:112:17
[unknown:0] Reset spawn flags: lastSpawnLatLng=null, loadedFromStorage=false logger.js:112:17
[unknown:0] Disabled storage sync during reset logger.js:112:17
[unknown:0] Spawning new Pokemon at location: 50.4922112, 10.8560384 logger.js:112:17
[unknown:0] [CACHE_DEBUG] Pre-loading landuse cache before spawning... logger.js:112:17
[unknown:0] Loading landuse data for area: 50492:10856:500 logger.js:112:17
[OVERPASS-CACHE] Cache miss, making API request for: 50.492,10.856,500 overpass-landuse.js:72:13
[OVERPASS-CACHE] API request completed in 726ms overpass-landuse.js:102:17
[OVERPASS-DEBUG] Received 7 elements from API overpass-landuse.js:103:17
[OVERPASS-DEBUG] Element types: way(4), relation(3) overpass-landuse.js:110:21
[OVERPASS-CACHE] Cached result for key: 50.492,10.856,500 overpass-landuse.js:45:11
[OVERPASS-CACHE] Processed 3 features and cached result overpass-landuse.js:164:17
[unknown:0] Cached landuse data with 3 features for area: 50492:10856:500 logger.js:112:17
[unknown:0] [CACHE_DEBUG] Pre-loaded cache with 3 features logger.js:112:17
[unknown:0] [STANDARD_SPAWN] 🎯 Starting spawnRandomPokemons with count=20 at 50.4922112,10.8560384 logger.js:138:17
[unknown:0] Created pokedex snapshot with 151 entries logger.js:112:17
[unknown:0] [LANDUSE_CACHE] 🗺️ Loading comprehensive landuse cache for 1km area: 50.4922112, 10.8560384 logger.js:138:17
[unknown:0] [LANDUSE_CACHE] 📡 Fetching ways + relations for landuse/natural/leisure from Overpass API... logger.js:138:17
[unknown:0] Loading landuse data for area: 50492:10856:1000 logger.js:112:17
[OVERPASS-CACHE] Cache miss, making API request for: 50.492,10.856,1000 overpass-landuse.js:72:13
[unknown:0] [LANDUSE_SPAWN] 🏞️ Starting spawnLanduseSpecialPokemons with count=20 at 50.4922112,10.8560384 logger.js:138:17
[unknown:0] Created pokedex snapshot for landuse spawning with 151 entries logger.js:112:17
[unknown:0] [LANDUSE_SPECIAL] 🗺️ Loading 1km landuse cache for special spawning: 50.4922112, 10.8560384 logger.js:112:17
[unknown:0] Loading landuse data for area: 50492:10856:1000 logger.js:112:17
[OVERPASS-CACHE] Cache miss, making API request for: 50.492,10.856,1000 overpass-landuse.js:72:13
[OVERPASS-CACHE] API request completed in 856ms overpass-landuse.js:102:17
[OVERPASS-DEBUG] Received 28 elements from API overpass-landuse.js:103:17
[OVERPASS-DEBUG] Element types: way(24), relation(4) overpass-landuse.js:110:21
[OVERPASS-CACHE] Cached result for key: 50.492,10.856,1000 overpass-landuse.js:45:11
[OVERPASS-CACHE] Processed 23 features and cached result overpass-landuse.js:164:17
[unknown:0] Cached landuse data with 23 features for area: 50492:10856:1000 logger.js:112:17
[unknown:0] Starting parallel landuse data collection for 20 Pokemon with grid distribution... logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 5 (exact match to team average) logger.js:112:17
[unknown:0] Creating new Pokemon gastly with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized gastly (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: gastly logger.js:112:17
[unknown:0] Pokemon family for gastly (chain 40): gastly (Dex #92, Evo level: 25), haunter (Dex #93, Evo level: 50), gengar (Dex #94, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdpthp7aeyqgqxv, base_name=gastly, name=gastly, level=5 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: gastly, dex: 92, evolution_level: 25}, {name: haunter, dex: 93, evolution_level: 50}, {name: gengar, dex: 94, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=gastly, dex=92, evolution_level=25 logger.js:112:17
[unknown:0] Grid cell (2557,18682) maps to chain index 23 of 78 (rarity: common) logger.js:112:17
[unknown:0] Accepted landuse spawn in grid 23 (1/5) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 5 (exact match to team average) logger.js:112:17
[unknown:0] Creating new Pokemon haunter with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized haunter (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: haunter logger.js:112:17
[unknown:0] Pokemon family for haunter (chain 40): gastly (Dex #92, Evo level: 25), haunter (Dex #93, Evo level: 50), gengar (Dex #94, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdpthp7bqlgcsc7, base_name=haunter, name=haunter, level=5 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: gastly, dex: 92, evolution_level: 25}, {name: haunter, dex: 93, evolution_level: 50}, {name: gengar, dex: 94, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=gastly, dex=92, evolution_level=25 logger.js:112:17
[unknown:0] Grid cell (2553,18681) maps to chain index 7 of 78 (rarity: common) logger.js:112:17
[unknown:0] Accepted landuse spawn in grid 7 (1/5) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 12 (team average 5 with deviation 7) logger.js:112:17
[unknown:0] Creating new Pokemon omanyte with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 12 (common): 1382 logger.js:112:17
[unknown:0] Initialized omanyte (Lvl 12) with 1382 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: omanyte logger.js:112:17
[unknown:0] Pokemon family for omanyte (chain 69): omanyte (Dex #138, Evo level: 40), omastar (Dex #139, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdpthp7cv1826v8, base_name=omanyte, name=omanyte, level=12 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: omanyte, dex: 138, evolution_level: 40}, {name: omastar, dex: 139, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=omanyte, dex=138, evolution_level=40 logger.js:112:17
[unknown:0] Grid cell (2555,18684) maps to chain index 52 of 78 (rarity: scarce) logger.js:112:17
[unknown:0] Accepted landuse spawn in grid 52 (1/5) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 1 (team average 5 with deviation -19) logger.js:112:17
[unknown:0] Creating new Pokemon gastly with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Found Pokemon by exact name match: gastly logger.js:112:17
[unknown:0] Pokemon family for gastly (chain 40): gastly (Dex #92, Evo level: 25), haunter (Dex #93, Evo level: 50), gengar (Dex #94, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdpthp7donktu9w, base_name=gastly, name=gastly, level=1 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: gastly, dex: 92, evolution_level: 25}, {name: haunter, dex: 93, evolution_level: 50}, {name: gengar, dex: 94, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=gastly, dex=92, evolution_level=25 logger.js:112:17
[unknown:0] Grid cell (2553,18681) maps to chain index 7 of 78 (rarity: common) logger.js:112:17
[unknown:0] Accepted landuse spawn in grid 7 (2/5) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 13 (team average 5 with deviation 8) logger.js:112:17
[unknown:0] Creating new Pokemon doduo with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 13 (common): 1757 logger.js:112:17
[unknown:0] Initialized doduo (Lvl 13) with 1757 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: doduo logger.js:112:17
[unknown:0] Pokemon family for doduo (chain 36): doduo (Dex #84, Evo level: 31), dodrio (Dex #85, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdpthp7eh8qlwsh, base_name=doduo, name=doduo, level=13 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: doduo, dex: 84, evolution_level: 31}, {name: dodrio, dex: 85, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=doduo, dex=84, evolution_level=31 logger.js:112:17
[unknown:0] Grid cell (2557,18680) maps to chain index 56 of 78 (rarity: rare) logger.js:112:17
[unknown:0] Accepted landuse spawn in grid 56 (1/5) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 3 (team average 5 with deviation -2) logger.js:112:17
[unknown:0] Creating new Pokemon rhyhorn with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 3 (common): 21 logger.js:112:17
[unknown:0] Initialized rhyhorn (Lvl 3) with 21 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: rhyhorn logger.js:112:17
[unknown:0] Pokemon family for rhyhorn (chain 50): rhyhorn (Dex #111, Evo level: 42), rhydon (Dex #112, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdpthp7eely8rfg, base_name=rhyhorn, name=rhyhorn, level=3 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: rhyhorn, dex: 111, evolution_level: 42}, {name: rhydon, dex: 112, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=rhyhorn, dex=111, evolution_level=42 logger.js:112:17
[unknown:0] Grid cell (2555,18684) maps to chain index 52 of 78 (rarity: scarce) logger.js:112:17
[unknown:0] Accepted landuse spawn in grid 52 (2/5) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 5 (exact match to team average) logger.js:112:17
[unknown:0] Creating new Pokemon exeggutor with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized exeggutor (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: exeggutor logger.js:112:17
[unknown:0] Pokemon family for exeggutor (chain 45): exeggcute (Dex #102, Evo level: 32), exeggutor (Dex #103, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdpthp7fcy1lxd2, base_name=exeggutor, name=exeggutor, level=5 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: exeggcute, dex: 102, evolution_level: 32}, {name: exeggutor, dex: 103, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=exeggcute, dex=102, evolution_level=32 logger.js:112:17
[unknown:0] Grid cell (2557,18683) maps to chain index 10 of 78 (rarity: common) logger.js:112:17
[unknown:0] Accepted landuse spawn in grid 10 (1/5) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 1 (team average 5 with deviation -16) logger.js:112:17
[unknown:0] Creating new Pokemon clefairy with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Found Pokemon by exact name match: clefairy logger.js:112:17
[unknown:0] Pokemon family for clefairy (chain 14): clefairy (Dex #35, Evo level: 32), clefable (Dex #36, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdpthp7gb83cyqj, base_name=clefairy, name=clefairy, level=1 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: clefairy, dex: 35, evolution_level: 32}, {name: clefable, dex: 36, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=clefairy, dex=35, evolution_level=32 logger.js:112:17
[unknown:0] Grid cell (2557,18683) maps to chain index 10 of 78 (rarity: common) logger.js:112:17
[unknown:0] Accepted landuse spawn in grid 10 (2/5) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 1 (team average 5 with deviation -6) logger.js:112:17
[unknown:0] Creating new Pokemon gloom with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Found Pokemon by exact name match: gloom logger.js:112:17
[unknown:0] Pokemon family for gloom (chain 18): oddish (Dex #43, Evo level: 21), gloom (Dex #44, Evo level: 42), vileplume (Dex #45, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdpthp7h971ykzn, base_name=gloom, name=gloom, level=1 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: oddish, dex: 43, evolution_level: 21}, {name: gloom, dex: 44, evolution_level: 42}, {name: vileplume, dex: 45, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=oddish, dex=43, evolution_level=21 logger.js:112:17
[unknown:0] Grid cell (2556,18683) maps to chain index 35 of 78 (rarity: common) logger.js:112:17
[unknown:0] Accepted landuse spawn in grid 35 (1/5) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 5 (exact match to team average) logger.js:112:17
[unknown:0] Creating new Pokemon haunter with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized haunter (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: haunter logger.js:112:17
[unknown:0] Pokemon family for haunter (chain 40): gastly (Dex #92, Evo level: 25), haunter (Dex #93, Evo level: 50), gengar (Dex #94, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdpthp7hvzimkci, base_name=haunter, name=haunter, level=5 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: gastly, dex: 92, evolution_level: 25}, {name: haunter, dex: 93, evolution_level: 50}, {name: gengar, dex: 94, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=gastly, dex=92, evolution_level=25 logger.js:112:17
[unknown:0] Grid cell (2557,18682) maps to chain index 23 of 78 (rarity: common) logger.js:112:17
[unknown:0] Accepted landuse spawn in grid 23 (2/5) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 6 (team average 5 with deviation 1) logger.js:112:17
[unknown:0] Creating new Pokemon gastly with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 6 (common): 172 logger.js:112:17
[unknown:0] Initialized gastly (Lvl 6) with 172 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: gastly logger.js:112:17
[unknown:0] Pokemon family for gastly (chain 40): gastly (Dex #92, Evo level: 25), haunter (Dex #93, Evo level: 50), gengar (Dex #94, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdpthp7i03091a5, base_name=gastly, name=gastly, level=6 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: gastly, dex: 92, evolution_level: 25}, {name: haunter, dex: 93, evolution_level: 50}, {name: gengar, dex: 94, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=gastly, dex=92, evolution_level=25 logger.js:112:17
[unknown:0] Grid cell (2553,18681) maps to chain index 7 of 78 (rarity: common) logger.js:112:17
[unknown:0] Accepted landuse spawn in grid 7 (3/5) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 10 (team average 5 with deviation 5) logger.js:112:17
[unknown:0] Creating new Pokemon clefable with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 10 (common): 800 logger.js:112:17
[unknown:0] Initialized clefable (Lvl 10) with 800 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: clefable logger.js:112:17
[unknown:0] Pokemon family for clefable (chain 14): clefairy (Dex #35, Evo level: 32), clefable (Dex #36, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdpthp7iflxknh0, base_name=clefable, name=clefable, level=10 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: clefairy, dex: 35, evolution_level: 32}, {name: clefable, dex: 36, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=clefairy, dex=35, evolution_level=32 logger.js:112:17
[unknown:0] Grid cell (2557,18683) maps to chain index 10 of 78 (rarity: common) logger.js:112:17
[unknown:0] Accepted landuse spawn in grid 10 (3/5) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 5 (exact match to team average) logger.js:112:17
[unknown:0] Creating new Pokemon magnemite with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized magnemite (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: magnemite logger.js:112:17
[unknown:0] Pokemon family for magnemite (chain 34): magnemite (Dex #81, Evo level: 30), magneton (Dex #82, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdpthp7jx0mgrt0, base_name=magnemite, name=magnemite, level=5 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: magnemite, dex: 81, evolution_level: 30}, {name: magneton, dex: 82, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=magnemite, dex=81, evolution_level=30 logger.js:112:17
[unknown:0] Grid cell (2554,18679) maps to chain index 61 of 78 (rarity: scarce) logger.js:112:17
[unknown:0] Accepted landuse spawn in grid 61 (1/5) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 6 (team average 5 with deviation 1) logger.js:112:17
[unknown:0] Creating new Pokemon exeggutor with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 6 (common): 172 logger.js:112:17
[unknown:0] Initialized exeggutor (Lvl 6) with 172 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: exeggutor logger.js:112:17
[unknown:0] Pokemon family for exeggutor (chain 45): exeggcute (Dex #102, Evo level: 32), exeggutor (Dex #103, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdpthp7kuiu5hok, base_name=exeggutor, name=exeggutor, level=6 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: exeggcute, dex: 102, evolution_level: 32}, {name: exeggutor, dex: 103, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=exeggcute, dex=102, evolution_level=32 logger.js:112:17
[unknown:0] Grid cell (2558,18681) maps to chain index 23 of 78 (rarity: common) logger.js:112:17
[unknown:0] Accepted landuse spawn in grid 23 (3/5) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 5 (exact match to team average) logger.js:112:17
[unknown:0] Creating new Pokemon oddish with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized oddish (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: oddish logger.js:112:17
[unknown:0] Pokemon family for oddish (chain 18): oddish (Dex #43, Evo level: 21), gloom (Dex #44, Evo level: 42), vileplume (Dex #45, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdpthp7kv4ekopk, base_name=oddish, name=oddish, level=5 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: oddish, dex: 43, evolution_level: 21}, {name: gloom, dex: 44, evolution_level: 42}, {name: vileplume, dex: 45, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=oddish, dex=43, evolution_level=21 logger.js:112:17
[unknown:0] Grid cell (2558,18683) maps to chain index 43 of 78 (rarity: common) logger.js:112:17
[unknown:0] Accepted landuse spawn in grid 43 (1/5) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 1 (team average 5 with deviation -18) logger.js:112:17
[unknown:0] Creating new Pokemon porygon with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Found Pokemon by exact name match: porygon logger.js:112:17
[unknown:0] Pokemon family for porygon (chain 68): porygon (Dex #137, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdpthp7lgp2grev, base_name=porygon, name=porygon, level=1 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: porygon, dex: 137, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=porygon, dex=137, evolution_level=none logger.js:112:17
[unknown:0] Grid cell (2552,18685) maps to chain index 47 of 78 (rarity: common) logger.js:112:17
[unknown:0] Accepted landuse spawn in grid 47 (1/5) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 1 (team average 5 with deviation -4) logger.js:112:17
[unknown:0] Creating new Pokemon onix with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Found Pokemon by exact name match: onix logger.js:112:17
[unknown:0] Pokemon family for onix (chain 41): onix (Dex #95, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdpthp7lce2hha5, base_name=onix, name=onix, level=1 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: onix, dex: 95, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=onix, dex=95, evolution_level=none logger.js:112:17
[unknown:0] Grid cell (2555,18684) maps to chain index 52 of 78 (rarity: scarce) logger.js:112:17
[unknown:0] Accepted landuse spawn in grid 52 (3/5) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 1 (team average 5 with deviation -4) logger.js:112:17
[unknown:0] Creating new Pokemon machop with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Found Pokemon by exact name match: machop logger.js:112:17
[unknown:0] Pokemon family for machop (chain 28): machop (Dex #66, Evo level: 28), machoke (Dex #67, Evo level: 56), machamp (Dex #68, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdpthp7nzqdsexv, base_name=machop, name=machop, level=1 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: machop, dex: 66, evolution_level: 28}, {name: machoke, dex: 67, evolution_level: 56}, {name: machamp, dex: 68, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=machop, dex=66, evolution_level=28 logger.js:112:17
[unknown:0] Grid cell (2555,18684) maps to chain index 52 of 78 (rarity: scarce) logger.js:112:17
[unknown:0] Accepted landuse spawn in grid 52 (4/5) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 1 (team average 5 with deviation -4) logger.js:112:17
[unknown:0] Creating new Pokemon graveler with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Found Pokemon by exact name match: graveler logger.js:112:17
[unknown:0] Pokemon family for graveler (chain 31): geodude (Dex #74, Evo level: 25), graveler (Dex #75, Evo level: 50), golem (Dex #76, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdpthp7nrrxuvg4, base_name=graveler, name=graveler, level=1 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: geodude, dex: 74, evolution_level: 25}, {name: graveler, dex: 75, evolution_level: 50}, {name: golem, dex: 76, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=geodude, dex=74, evolution_level=25 logger.js:112:17
[unknown:0] Grid cell (2555,18684) maps to chain index 52 of 78 (rarity: scarce) logger.js:112:17
[unknown:0] Accepted landuse spawn in grid 52 (5/5) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 13 (team average 5 with deviation 8) logger.js:112:17
[unknown:0] Creating new Pokemon clefairy with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 13 (common): 1757 logger.js:112:17
[unknown:0] Initialized clefairy (Lvl 13) with 1757 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: clefairy logger.js:112:17
[unknown:0] Pokemon family for clefairy (chain 14): clefairy (Dex #35, Evo level: 32), clefable (Dex #36, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdpthp7os9j0h7i, base_name=clefairy, name=clefairy, level=13 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: clefairy, dex: 35, evolution_level: 32}, {name: clefable, dex: 36, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=clefairy, dex=35, evolution_level=32 logger.js:112:17
[unknown:0] Grid cell (2557,18683) maps to chain index 10 of 78 (rarity: common) logger.js:112:17
[unknown:0] Accepted landuse spawn in grid 10 (4/5) logger.js:112:17
[unknown:0] Landuse grid distribution summary: 9 cells used, max per cell: 5 logger.js:112:17
[unknown:0]   Landuse Grid 23: 3 Pokemon logger.js:112:17
[unknown:0]   Landuse Grid 7: 3 Pokemon logger.js:112:17
[unknown:0]   Landuse Grid 52: 5 Pokemon logger.js:112:17
[unknown:0]   Landuse Grid 56: 1 Pokemon logger.js:112:17
[unknown:0]   Landuse Grid 10: 4 Pokemon logger.js:112:17
[unknown:0]   Landuse Grid 35: 1 Pokemon logger.js:112:17
[unknown:0]   Landuse Grid 61: 1 Pokemon logger.js:112:17
[unknown:0]   Landuse Grid 43: 1 Pokemon logger.js:112:17
[unknown:0]   Landuse Grid 47: 1 Pokemon logger.js:112:17
[unknown:0] Creating new Pokemon gastly with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized gastly (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] Created final Pokemon: gastly (base: gastly) Level 5, Dex #92 [Landuse Special] logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon haunter with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized haunter (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] Created final Pokemon: gastly (base: haunter) Level 5, Dex #92 [Landuse Special] logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon omanyte with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 12 (common): 1382 logger.js:112:17
[unknown:0] Initialized omanyte (Lvl 12) with 1382 XP logger.js:112:17
[unknown:0] Created final Pokemon: omanyte (base: omanyte) Level 12, Dex #138 [Landuse Special] logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon gastly with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Created final Pokemon: gastly (base: gastly) Level 1, Dex #92 [Landuse Special] logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon doduo with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 13 (common): 1757 logger.js:112:17
[unknown:0] Initialized doduo (Lvl 13) with 1757 XP logger.js:112:17
[unknown:0] Created final Pokemon: doduo (base: doduo) Level 13, Dex #84 [Landuse Special] logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon rhyhorn with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 3 (common): 21 logger.js:112:17
[unknown:0] Initialized rhyhorn (Lvl 3) with 21 XP logger.js:112:17
[unknown:0] Created final Pokemon: rhyhorn (base: rhyhorn) Level 3, Dex #111 [Landuse Special] logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon exeggutor with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized exeggutor (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] Created final Pokemon: exeggcute (base: exeggutor) Level 5, Dex #102 [Landuse Special] logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon clefairy with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Created final Pokemon: clefairy (base: clefairy) Level 1, Dex #35 [Landuse Special] logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon gloom with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Created final Pokemon: oddish (base: gloom) Level 1, Dex #43 [Landuse Special] logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon haunter with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized haunter (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] Created final Pokemon: gastly (base: haunter) Level 5, Dex #92 [Landuse Special] logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon gastly with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 6 (common): 172 logger.js:112:17
[unknown:0] Initialized gastly (Lvl 6) with 172 XP logger.js:112:17
[unknown:0] Created final Pokemon: gastly (base: gastly) Level 6, Dex #92 [Landuse Special] logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon clefable with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 10 (common): 800 logger.js:112:17
[unknown:0] Initialized clefable (Lvl 10) with 800 XP logger.js:112:17
[unknown:0] Created final Pokemon: clefairy (base: clefable) Level 10, Dex #35 [Landuse Special] logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon magnemite with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized magnemite (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] Created final Pokemon: magnemite (base: magnemite) Level 5, Dex #81 [Landuse Special] logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon exeggutor with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 6 (common): 172 logger.js:112:17
[unknown:0] Initialized exeggutor (Lvl 6) with 172 XP logger.js:112:17
[unknown:0] Created final Pokemon: exeggcute (base: exeggutor) Level 6, Dex #102 [Landuse Special] logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon oddish with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized oddish (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] Created final Pokemon: oddish (base: oddish) Level 5, Dex #43 [Landuse Special] logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon porygon with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Created final Pokemon: porygon (base: porygon) Level 1, Dex #137 [Landuse Special] logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon onix with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Created final Pokemon: onix (base: onix) Level 1, Dex #95 [Landuse Special] logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon machop with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Created final Pokemon: machop (base: machop) Level 1, Dex #66 [Landuse Special] logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon graveler with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Created final Pokemon: geodude (base: graveler) Level 1, Dex #74 [Landuse Special] logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon clefairy with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 13 (common): 1757 logger.js:112:17
[unknown:0] Initialized clefairy (Lvl 13) with 1757 XP logger.js:112:17
[unknown:0] Created final Pokemon: clefairy (base: clefairy) Level 13, Dex #35 [Landuse Special] logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] [LANDUSE_SPAWN] ✅ Successfully created 20 landuse Pokemon with isolated spawning system logger.js:138:17
[OVERPASS-CACHE] API request completed in 1114ms overpass-landuse.js:102:17
[OVERPASS-DEBUG] Received 28 elements from API overpass-landuse.js:103:17
[OVERPASS-DEBUG] Element types: way(24), relation(4) overpass-landuse.js:110:21
[OVERPASS-CACHE] Cached result for key: 50.492,10.856,1000 overpass-landuse.js:45:11
[OVERPASS-CACHE] Processed 23 features and cached result overpass-landuse.js:164:17
[unknown:0] Cached landuse data with 23 features for area: 50492:10856:1000 logger.js:112:17
[unknown:0] [LANDUSE_CACHE] ✅ Landuse cache loaded with 23 total features logger.js:138:17
[unknown:0] [LANDUSE_CACHE] 📊 Feature analysis: 23/23 valid features logger.js:138:17
[unknown:0] [LANDUSE_CACHE] 🏷️ Feature types: residential(5), cemetery(2), allotments(1), industrial(2), wood(5), grass(1), playground(1), pitch(1), meadow(4), cliff(1) logger.js:138:17
[unknown:0] [STANDARD_SPAWN] 🎯 Starting intelligent spawn collection for 20 Pokemon with landuse optimization... logger.js:138:17
[unknown:0] Grid cell (2555,18682) maps to chain index 30 of 78 (rarity: common) logger.js:112:17
[unknown:0] Found base Pokemon for chain 31: geodude (74) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 6 (team average 5 with deviation 1) logger.js:112:17
[unknown:0] Creating new Pokemon geodude with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 6 (common): 172 logger.js:112:17
[unknown:0] Initialized geodude (Lvl 6) with 172 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: geodude logger.js:112:17
[unknown:0] Pokemon family for geodude (chain 31): geodude (Dex #74, Evo level: 25), graveler (Dex #75, Evo level: 50), golem (Dex #76, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdpthpeg17mtcy5, base_name=geodude, name=geodude, level=6 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: geodude, dex: 74, evolution_level: 25}, {name: graveler, dex: 75, evolution_level: 50}, {name: golem, dex: 76, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=geodude, dex=74, evolution_level=25 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] Starting landuse lookup for geodude at 50.49353930577486,10.857107256433737 logger.js:112:17
[unknown:0] [LANDUSE_LOOKUP] Starting landuse lookup for point 50.49353930577486, 10.857107256433737 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] Checking 23 landuse features for point 50.49353930577486, 10.857107256433737 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] ❌ No valid landuse match found for point 50.49353930577486, 10.857107256433737 logger.js:138:17
[unknown:0] [LANDUSE_DEBUG] Landuse lookup completed for geodude: logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache available: YES logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache features: 23 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Landuse result: null logger.js:112:17
[unknown:0] [LANDUSE_MISS] ⚪ Standard spawn without landuse (normal for random spawns) logger.js:112:17
[unknown:0] Grid cell (2555,18682) maps to chain index 30 of 78 (rarity: common) logger.js:112:17
[unknown:0] [GRID] Accepted spawn in grid 30 (1/5) logger.js:112:17
[unknown:0] Grid cell (2553,18682) maps to chain index 12 of 78 (rarity: common) logger.js:112:17
[unknown:0] Found base Pokemon for chain 13: nidoran-m (32) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 5 (exact match to team average) logger.js:112:17
[unknown:0] Creating new Pokemon nidoran-m with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized nidoran-m (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: nidoran-m logger.js:112:17
[unknown:0] Pokemon family for nidoran-m (chain 13): nidoran-m (Dex #32, Evo level: 16), nidorino (Dex #33, Evo level: 32), nidoking (Dex #34, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdpthpegwrooyd2, base_name=nidoran-m, name=nidoran-m, level=5 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: nidoran-m, dex: 32, evolution_level: 16}, {name: nidorino, dex: 33, evolution_level: 32}, {name: nidoking, dex: 34, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=nidoran-m, dex=32, evolution_level=16 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] Starting landuse lookup for nidoran-m at 50.49356472751519,10.850000315558779 logger.js:112:17
[unknown:0] [LANDUSE_LOOKUP] Starting landuse lookup for point 50.49356472751519, 10.850000315558779 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] Checking 23 landuse features for point 50.49356472751519, 10.850000315558779 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] ❌ No valid landuse match found for point 50.49356472751519, 10.850000315558779 logger.js:138:17
[unknown:0] [LANDUSE_DEBUG] Landuse lookup completed for nidoran-m: logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache available: YES logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache features: 23 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Landuse result: null logger.js:112:17
[unknown:0] [LANDUSE_MISS] ⚪ Standard spawn without landuse (normal for random spawns) logger.js:112:17
[unknown:0] Grid cell (2553,18682) maps to chain index 12 of 78 (rarity: common) logger.js:112:17
[unknown:0] [GRID] Accepted spawn in grid 12 (1/5) logger.js:112:17
[unknown:0] Grid cell (2556,18682) maps to chain index 21 of 78 (rarity: common) logger.js:112:17
[unknown:0] Found base Pokemon for chain 22: meowth (52) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 6 (team average 5 with deviation 1) logger.js:112:17
[unknown:0] Creating new Pokemon meowth with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 6 (common): 172 logger.js:112:17
[unknown:0] Initialized meowth (Lvl 6) with 172 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: meowth logger.js:112:17
[unknown:0] Pokemon family for meowth (chain 22): meowth (Dex #52, Evo level: 28), persian (Dex #53, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdpthpehykawy6l, base_name=meowth, name=meowth, level=6 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: meowth, dex: 52, evolution_level: 28}, {name: persian, dex: 53, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=meowth, dex=52, evolution_level=28 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] Starting landuse lookup for meowth at 50.492874741188444,10.858863304054294 logger.js:112:17
[unknown:0] [LANDUSE_LOOKUP] Starting landuse lookup for point 50.492874741188444, 10.858863304054294 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] Checking 23 landuse features for point 50.492874741188444, 10.858863304054294 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] ❌ No valid landuse match found for point 50.492874741188444, 10.858863304054294 logger.js:138:17
[unknown:0] [LANDUSE_DEBUG] Landuse lookup completed for meowth: logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache available: YES logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache features: 23 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Landuse result: null logger.js:112:17
[unknown:0] [LANDUSE_MISS] ⚪ Standard spawn without landuse (normal for random spawns) logger.js:112:17
[unknown:0] Grid cell (2556,18682) maps to chain index 21 of 78 (rarity: common) logger.js:112:17
[unknown:0] [GRID] Accepted spawn in grid 21 (1/5) logger.js:112:17
[unknown:0] Grid cell (2554,18682) maps to chain index 28 of 78 (rarity: common) logger.js:112:17
[unknown:0] Found base Pokemon for chain 29: bellsprout (69) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 1 (team average 5 with deviation -14) logger.js:112:17
[unknown:0] Creating new Pokemon bellsprout with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Found Pokemon by exact name match: bellsprout logger.js:112:17
[unknown:0] Pokemon family for bellsprout (chain 29): bellsprout (Dex #69, Evo level: 21), weepinbell (Dex #70, Evo level: 42), victreebel (Dex #71, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdpthpeie603j7f, base_name=bellsprout, name=bellsprout, level=1 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: bellsprout, dex: 69, evolution_level: 21}, {name: weepinbell, dex: 70, evolution_level: 42}, {name: victreebel, dex: 71, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=bellsprout, dex=69, evolution_level=21 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] Starting landuse lookup for bellsprout at 50.4941972117176,10.853798899287353 logger.js:112:17
[unknown:0] [LANDUSE_LOOKUP] Starting landuse lookup for point 50.4941972117176, 10.853798899287353 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] Checking 23 landuse features for point 50.4941972117176, 10.853798899287353 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] ❌ No valid landuse match found for point 50.4941972117176, 10.853798899287353 logger.js:138:17
[unknown:0] [LANDUSE_DEBUG] Landuse lookup completed for bellsprout: logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache available: YES logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache features: 23 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Landuse result: null logger.js:112:17
[unknown:0] [LANDUSE_MISS] ⚪ Standard spawn without landuse (normal for random spawns) logger.js:112:17
[unknown:0] Grid cell (2554,18682) maps to chain index 28 of 78 (rarity: common) logger.js:112:17
[unknown:0] [GRID] Accepted spawn in grid 28 (1/5) logger.js:112:17
[unknown:0] Grid cell (2556,18682) maps to chain index 21 of 78 (rarity: common) logger.js:112:17
[unknown:0] Found base Pokemon for chain 22: meowth (52) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 5 (exact match to team average) logger.js:112:17
[unknown:0] Creating new Pokemon meowth with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized meowth (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: meowth logger.js:112:17
[unknown:0] Pokemon family for meowth (chain 22): meowth (Dex #52, Evo level: 28), persian (Dex #53, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdpthpejijnb3gc, base_name=meowth, name=meowth, level=5 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: meowth, dex: 52, evolution_level: 28}, {name: persian, dex: 53, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=meowth, dex=52, evolution_level=28 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] Starting landuse lookup for meowth at 50.49226643285825,10.860372373596359 logger.js:112:17
[unknown:0] [LANDUSE_LOOKUP] Starting landuse lookup for point 50.49226643285825, 10.860372373596359 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] Checking 23 landuse features for point 50.49226643285825, 10.860372373596359 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] ❌ No valid landuse match found for point 50.49226643285825, 10.860372373596359 logger.js:138:17
[unknown:0] [LANDUSE_DEBUG] Landuse lookup completed for meowth: logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache available: YES logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache features: 23 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Landuse result: null logger.js:112:17
[unknown:0] [LANDUSE_MISS] ⚪ Standard spawn without landuse (normal for random spawns) logger.js:112:17
[unknown:0] Grid cell (2556,18682) maps to chain index 21 of 78 (rarity: common) logger.js:112:17
[unknown:0] [GRID] Accepted spawn in grid 21 (2/5) logger.js:112:17
[unknown:0] Grid cell (2554,18681) maps to chain index 36 of 78 (rarity: common) logger.js:112:17
[unknown:0] Found base Pokemon for chain 37: seel (86) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 6 (team average 5 with deviation 1) logger.js:112:17
[unknown:0] Creating new Pokemon seel with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 6 (common): 172 logger.js:112:17
[unknown:0] Initialized seel (Lvl 6) with 172 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: seel logger.js:112:17
[unknown:0] Pokemon family for seel (chain 37): seel (Dex #86, Evo level: 34), dewgong (Dex #87, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdpthpekkcrrmzc, base_name=seel, name=seel, level=6 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: seel, dex: 86, evolution_level: 34}, {name: dewgong, dex: 87, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=seel, dex=86, evolution_level=34 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] Starting landuse lookup for seel at 50.489469965069105,10.850465878597495 logger.js:112:17
[unknown:0] [LANDUSE_LOOKUP] Starting landuse lookup for point 50.489469965069105, 10.850465878597495 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] Checking 23 landuse features for point 50.489469965069105, 10.850465878597495 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] ❌ No valid landuse match found for point 50.489469965069105, 10.850465878597495 logger.js:138:17
[unknown:0] [LANDUSE_DEBUG] Landuse lookup completed for seel: logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache available: YES logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache features: 23 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Landuse result: null logger.js:112:17
[unknown:0] [LANDUSE_MISS] ⚪ Standard spawn without landuse (normal for random spawns) logger.js:112:17
[unknown:0] Grid cell (2554,18681) maps to chain index 36 of 78 (rarity: common) logger.js:112:17
[unknown:0] [GRID] Accepted spawn in grid 36 (1/5) logger.js:112:17
[unknown:0] Grid cell (2556,18680) maps to chain index 40 of 78 (rarity: common) logger.js:112:17
[unknown:0] Found base Pokemon for chain 41: onix (95) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 3 (team average 5 with deviation -2) logger.js:112:17
[unknown:0] Creating new Pokemon onix with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 3 (common): 21 logger.js:112:17
[unknown:0] Initialized onix (Lvl 3) with 21 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: onix logger.js:112:17
[unknown:0] Pokemon family for onix (chain 41): onix (Dex #95, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdpthpelnbz2y2a, base_name=onix, name=onix, level=3 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: onix, dex: 95, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=onix, dex=95, evolution_level=none logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] Starting landuse lookup for onix at 50.488347680557574,10.859323440013428 logger.js:112:17
[unknown:0] [LANDUSE_LOOKUP] Starting landuse lookup for point 50.488347680557574, 10.859323440013428 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] Checking 23 landuse features for point 50.488347680557574, 10.859323440013428 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] ❌ No valid landuse match found for point 50.488347680557574, 10.859323440013428 logger.js:138:17
[unknown:0] [LANDUSE_DEBUG] Landuse lookup completed for onix: logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache available: YES logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache features: 23 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Landuse result: null logger.js:112:17
[unknown:0] [LANDUSE_MISS] ⚪ Standard spawn without landuse (normal for random spawns) logger.js:112:17
[unknown:0] Grid cell (2556,18680) maps to chain index 40 of 78 (rarity: common) logger.js:112:17
[unknown:0] [GRID] Accepted spawn in grid 40 (1/5) logger.js:112:17
[unknown:0] Grid cell (2553,18682) maps to chain index 12 of 78 (rarity: common) logger.js:112:17
[unknown:0] Found base Pokemon for chain 13: nidoran-m (32) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 9 (team average 5 with deviation 4) logger.js:112:17
[unknown:0] Creating new Pokemon nidoran-m with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 9 (common): 583 logger.js:112:17
[unknown:0] Initialized nidoran-m (Lvl 9) with 583 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: nidoran-m logger.js:112:17
[unknown:0] Pokemon family for nidoran-m (chain 13): nidoran-m (Dex #32, Evo level: 16), nidorino (Dex #33, Evo level: 32), nidoking (Dex #34, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdpthpemsywknrq, base_name=nidoran-m, name=nidoran-m, level=9 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: nidoran-m, dex: 32, evolution_level: 16}, {name: nidorino, dex: 33, evolution_level: 32}, {name: nidoking, dex: 34, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=nidoran-m, dex=32, evolution_level=16 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] Starting landuse lookup for nidoran-m at 50.49354478559857,10.850226321664824 logger.js:112:17
[unknown:0] [LANDUSE_LOOKUP] Starting landuse lookup for point 50.49354478559857, 10.850226321664824 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] Checking 23 landuse features for point 50.49354478559857, 10.850226321664824 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] ❌ No valid landuse match found for point 50.49354478559857, 10.850226321664824 logger.js:138:17
[unknown:0] [LANDUSE_DEBUG] Landuse lookup completed for nidoran-m: logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache available: YES logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache features: 23 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Landuse result: null logger.js:112:17
[unknown:0] [LANDUSE_MISS] ⚪ Standard spawn without landuse (normal for random spawns) logger.js:112:17
[unknown:0] Grid cell (2553,18682) maps to chain index 12 of 78 (rarity: common) logger.js:112:17
[unknown:0] [GRID] Accepted spawn in grid 12 (2/5) logger.js:112:17
[unknown:0] Grid cell (2555,18681) maps to chain index 3 of 78 (rarity: common) logger.js:112:17
[unknown:0] Found base Pokemon for chain 4: caterpie (10) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 5 (exact match to team average) logger.js:112:17
[unknown:0] Creating new Pokemon caterpie with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized caterpie (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: caterpie logger.js:112:17
[unknown:0] Pokemon family for caterpie (chain 4): caterpie (Dex #10, Evo level: 7), metapod (Dex #11, Evo level: 10), butterfree (Dex #12, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdpthpen7kb3xef, base_name=caterpie, name=caterpie, level=5 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: caterpie, dex: 10, evolution_level: 7}, {name: metapod, dex: 11, evolution_level: 10}, {name: butterfree, dex: 12, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=caterpie, dex=10, evolution_level=7 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] Starting landuse lookup for caterpie at 50.49187817727295,10.858129371384278 logger.js:112:17
[unknown:0] [LANDUSE_LOOKUP] Starting landuse lookup for point 50.49187817727295, 10.858129371384278 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] Checking 23 landuse features for point 50.49187817727295, 10.858129371384278 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] ❌ No valid landuse match found for point 50.49187817727295, 10.858129371384278 logger.js:138:17
[unknown:0] [LANDUSE_DEBUG] Landuse lookup completed for caterpie: logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache available: YES logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache features: 23 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Landuse result: null logger.js:112:17
[unknown:0] [LANDUSE_MISS] ⚪ Standard spawn without landuse (normal for random spawns) logger.js:112:17
[unknown:0] Grid cell (2555,18681) maps to chain index 3 of 78 (rarity: common) logger.js:112:17
[unknown:0] [GRID] Accepted spawn in grid 3 (1/5) logger.js:112:17
[unknown:0] Grid cell (2555,18682) maps to chain index 30 of 78 (rarity: common) logger.js:112:17
[unknown:0] Found base Pokemon for chain 31: geodude (74) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 5 (exact match to team average) logger.js:112:17
[unknown:0] Creating new Pokemon geodude with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized geodude (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: geodude logger.js:112:17
[unknown:0] Pokemon family for geodude (chain 31): geodude (Dex #74, Evo level: 25), graveler (Dex #75, Evo level: 50), golem (Dex #76, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdpthpeo9i9f2dz, base_name=geodude, name=geodude, level=5 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: geodude, dex: 74, evolution_level: 25}, {name: graveler, dex: 75, evolution_level: 50}, {name: golem, dex: 76, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=geodude, dex=74, evolution_level=25 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] Starting landuse lookup for geodude at 50.49356872046818,10.855479525761162 logger.js:112:17
[unknown:0] [LANDUSE_LOOKUP] Starting landuse lookup for point 50.49356872046818, 10.855479525761162 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] Checking 23 landuse features for point 50.49356872046818, 10.855479525761162 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] ❌ No valid landuse match found for point 50.49356872046818, 10.855479525761162 logger.js:138:17
[unknown:0] [LANDUSE_DEBUG] Landuse lookup completed for geodude: logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache available: YES logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache features: 23 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Landuse result: null logger.js:112:17
[unknown:0] [LANDUSE_MISS] ⚪ Standard spawn without landuse (normal for random spawns) logger.js:112:17
[unknown:0] Grid cell (2555,18682) maps to chain index 30 of 78 (rarity: common) logger.js:112:17
[unknown:0] [GRID] Accepted spawn in grid 30 (2/5) logger.js:112:17
[unknown:0] Grid cell (2555,18681) maps to chain index 3 of 78 (rarity: common) logger.js:112:17
[unknown:0] Found base Pokemon for chain 4: caterpie (10) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 1 (team average 5 with deviation -8) logger.js:112:17
[unknown:0] Creating new Pokemon caterpie with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Found Pokemon by exact name match: caterpie logger.js:112:17
[unknown:0] Pokemon family for caterpie (chain 4): caterpie (Dex #10, Evo level: 7), metapod (Dex #11, Evo level: 10), butterfree (Dex #12, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdpthpepzozwspf, base_name=caterpie, name=caterpie, level=1 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: caterpie, dex: 10, evolution_level: 7}, {name: metapod, dex: 11, evolution_level: 10}, {name: butterfree, dex: 12, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=caterpie, dex=10, evolution_level=7 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] Starting landuse lookup for caterpie at 50.49032041177271,10.856869508882909 logger.js:112:17
[unknown:0] [LANDUSE_LOOKUP] Starting landuse lookup for point 50.49032041177271, 10.856869508882909 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] Checking 23 landuse features for point 50.49032041177271, 10.856869508882909 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] ❌ No valid landuse match found for point 50.49032041177271, 10.856869508882909 logger.js:138:17
[unknown:0] [LANDUSE_DEBUG] Landuse lookup completed for caterpie: logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache available: YES logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache features: 23 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Landuse result: null logger.js:112:17
[unknown:0] [LANDUSE_MISS] ⚪ Standard spawn without landuse (normal for random spawns) logger.js:112:17
[unknown:0] Grid cell (2555,18681) maps to chain index 3 of 78 (rarity: common) logger.js:112:17
[unknown:0] [GRID] Accepted spawn in grid 3 (2/5) logger.js:112:17
[unknown:0] Grid cell (2555,18682) maps to chain index 30 of 78 (rarity: common) logger.js:112:17
[unknown:0] Found base Pokemon for chain 31: geodude (74) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 5 (exact match to team average) logger.js:112:17
[unknown:0] Creating new Pokemon geodude with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized geodude (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: geodude logger.js:112:17
[unknown:0] Pokemon family for geodude (chain 31): geodude (Dex #74, Evo level: 25), graveler (Dex #75, Evo level: 50), golem (Dex #76, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdpthpepwzypq64, base_name=geodude, name=geodude, level=5 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: geodude, dex: 74, evolution_level: 25}, {name: graveler, dex: 75, evolution_level: 50}, {name: golem, dex: 76, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=geodude, dex=74, evolution_level=25 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] Starting landuse lookup for geodude at 50.49208205892965,10.855085348959538 logger.js:112:17
[unknown:0] [LANDUSE_LOOKUP] Starting landuse lookup for point 50.49208205892965, 10.855085348959538 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] Checking 23 landuse features for point 50.49208205892965, 10.855085348959538 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] ❌ No valid landuse match found for point 50.49208205892965, 10.855085348959538 logger.js:138:17
[unknown:0] [LANDUSE_DEBUG] Landuse lookup completed for geodude: logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache available: YES logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache features: 23 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Landuse result: null logger.js:112:17
[unknown:0] [LANDUSE_MISS] ⚪ Standard spawn without landuse (normal for random spawns) logger.js:112:17
[unknown:0] Grid cell (2555,18682) maps to chain index 30 of 78 (rarity: common) logger.js:112:17
[unknown:0] [GRID] Accepted spawn in grid 30 (3/5) logger.js:112:17
[unknown:0] Grid cell (2554,18680) maps to chain index 37 of 78 (rarity: common) logger.js:112:17
[unknown:0] Found base Pokemon for chain 38: grimer (88) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 5 (exact match to team average) logger.js:112:17
[unknown:0] Creating new Pokemon grimer with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized grimer (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: grimer logger.js:112:17
[unknown:0] Pokemon family for grimer (chain 38): grimer (Dex #88, Evo level: 38), muk (Dex #89, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdpthpeqnusuxqz, base_name=grimer, name=grimer, level=5 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: grimer, dex: 88, evolution_level: 38}, {name: muk, dex: 89, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=grimer, dex=88, evolution_level=38 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] Starting landuse lookup for grimer at 50.48872786606624,10.852861114705732 logger.js:112:17
[unknown:0] [LANDUSE_LOOKUP] Starting landuse lookup for point 50.48872786606624, 10.852861114705732 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] Checking 23 landuse features for point 50.48872786606624, 10.852861114705732 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] ❌ No valid landuse match found for point 50.48872786606624, 10.852861114705732 logger.js:138:17
[unknown:0] [LANDUSE_DEBUG] Landuse lookup completed for grimer: logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache available: YES logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache features: 23 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Landuse result: null logger.js:112:17
[unknown:0] [LANDUSE_MISS] ⚪ Standard spawn without landuse (normal for random spawns) logger.js:112:17
[unknown:0] Grid cell (2554,18680) maps to chain index 37 of 78 (rarity: common) logger.js:112:17
[unknown:0] [GRID] Accepted spawn in grid 37 (1/5) logger.js:112:17
[unknown:0] Grid cell (2554,18681) maps to chain index 36 of 78 (rarity: common) logger.js:112:17
[unknown:0] Found base Pokemon for chain 37: seel (86) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 5 (exact match to team average) logger.js:112:17
[unknown:0] Creating new Pokemon seel with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized seel (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: seel logger.js:112:17
[unknown:0] Pokemon family for seel (chain 37): seel (Dex #86, Evo level: 34), dewgong (Dex #87, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdpthperdcjuo8p, base_name=seel, name=seel, level=5 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: seel, dex: 86, evolution_level: 34}, {name: dewgong, dex: 87, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=seel, dex=86, evolution_level=34 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] Starting landuse lookup for seel at 50.49167376240487,10.852908879805424 logger.js:112:17
[unknown:0] [LANDUSE_LOOKUP] Starting landuse lookup for point 50.49167376240487, 10.852908879805424 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] Checking 23 landuse features for point 50.49167376240487, 10.852908879805424 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] ❌ No valid landuse match found for point 50.49167376240487, 10.852908879805424 logger.js:138:17
[unknown:0] [LANDUSE_DEBUG] Landuse lookup completed for seel: logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache available: YES logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache features: 23 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Landuse result: null logger.js:112:17
[unknown:0] [LANDUSE_MISS] ⚪ Standard spawn without landuse (normal for random spawns) logger.js:112:17
[unknown:0] Grid cell (2554,18681) maps to chain index 36 of 78 (rarity: common) logger.js:112:17
[unknown:0] [GRID] Accepted spawn in grid 36 (2/5) logger.js:112:17
[unknown:0] Grid cell (2555,18682) maps to chain index 30 of 78 (rarity: common) logger.js:112:17
[unknown:0] Found base Pokemon for chain 31: geodude (74) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 4 (team average 5 with deviation -1) logger.js:112:17
[unknown:0] Creating new Pokemon geodude with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 4 (common): 51 logger.js:112:17
[unknown:0] Initialized geodude (Lvl 4) with 51 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: geodude logger.js:112:17
[unknown:0] Pokemon family for geodude (chain 31): geodude (Dex #74, Evo level: 25), graveler (Dex #75, Evo level: 50), golem (Dex #76, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdpthpeskzzzmu3, base_name=geodude, name=geodude, level=4 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: geodude, dex: 74, evolution_level: 25}, {name: graveler, dex: 75, evolution_level: 50}, {name: golem, dex: 76, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=geodude, dex=74, evolution_level=25 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] Starting landuse lookup for geodude at 50.49225119036792,10.857371986338443 logger.js:112:17
[unknown:0] [LANDUSE_LOOKUP] Starting landuse lookup for point 50.49225119036792, 10.857371986338443 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] Checking 23 landuse features for point 50.49225119036792, 10.857371986338443 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] ❌ No valid landuse match found for point 50.49225119036792, 10.857371986338443 logger.js:138:17
[unknown:0] [LANDUSE_DEBUG] Landuse lookup completed for geodude: logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache available: YES logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache features: 23 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Landuse result: null logger.js:112:17
[unknown:0] [LANDUSE_MISS] ⚪ Standard spawn without landuse (normal for random spawns) logger.js:112:17
[unknown:0] Grid cell (2555,18682) maps to chain index 30 of 78 (rarity: common) logger.js:112:17
[unknown:0] [GRID] Accepted spawn in grid 30 (4/5) logger.js:112:17
[unknown:0] Grid cell (2555,18682) maps to chain index 30 of 78 (rarity: common) logger.js:112:17
[unknown:0] Found base Pokemon for chain 31: geodude (74) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 6 (team average 5 with deviation 1) logger.js:112:17
[unknown:0] Creating new Pokemon geodude with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 6 (common): 172 logger.js:112:17
[unknown:0] Initialized geodude (Lvl 6) with 172 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: geodude logger.js:112:17
[unknown:0] Pokemon family for geodude (chain 31): geodude (Dex #74, Evo level: 25), graveler (Dex #75, Evo level: 50), golem (Dex #76, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdpthpet5ovpp9n, base_name=geodude, name=geodude, level=6 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: geodude, dex: 74, evolution_level: 25}, {name: graveler, dex: 75, evolution_level: 50}, {name: golem, dex: 76, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=geodude, dex=74, evolution_level=25 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] Starting landuse lookup for geodude at 50.49332532400175,10.85491329091027 logger.js:112:17
[unknown:0] [LANDUSE_LOOKUP] Starting landuse lookup for point 50.49332532400175, 10.85491329091027 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] Checking 23 landuse features for point 50.49332532400175, 10.85491329091027 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] ❌ No valid landuse match found for point 50.49332532400175, 10.85491329091027 logger.js:138:17
[unknown:0] [LANDUSE_DEBUG] Landuse lookup completed for geodude: logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache available: YES logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache features: 23 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Landuse result: null logger.js:112:17
[unknown:0] [LANDUSE_MISS] ⚪ Standard spawn without landuse (normal for random spawns) logger.js:112:17
[unknown:0] Grid cell (2555,18682) maps to chain index 30 of 78 (rarity: common) logger.js:112:17
[unknown:0] [GRID] Accepted spawn in grid 30 (5/5) logger.js:112:17
[unknown:0] Grid cell (2554,18681) maps to chain index 36 of 78 (rarity: common) logger.js:112:17
[unknown:0] Found base Pokemon for chain 37: seel (86) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 14 (team average 5 with deviation 9) logger.js:112:17
[unknown:0] Creating new Pokemon seel with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 14 (common): 2195 logger.js:112:17
[unknown:0] Initialized seel (Lvl 14) with 2195 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: seel logger.js:112:17
[unknown:0] Pokemon family for seel (chain 37): seel (Dex #86, Evo level: 34), dewgong (Dex #87, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdpthpeulfpb6p9, base_name=seel, name=seel, level=14 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: seel, dex: 86, evolution_level: 34}, {name: dewgong, dex: 87, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=seel, dex=86, evolution_level=34 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] Starting landuse lookup for seel at 50.49002087693394,10.853792179901614 logger.js:112:17
[unknown:0] [LANDUSE_LOOKUP] Starting landuse lookup for point 50.49002087693394, 10.853792179901614 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] Checking 23 landuse features for point 50.49002087693394, 10.853792179901614 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] ❌ No valid landuse match found for point 50.49002087693394, 10.853792179901614 logger.js:138:17
[unknown:0] [LANDUSE_DEBUG] Landuse lookup completed for seel: logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache available: YES logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache features: 23 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Landuse result: null logger.js:112:17
[unknown:0] [LANDUSE_MISS] ⚪ Standard spawn without landuse (normal for random spawns) logger.js:112:17
[unknown:0] Grid cell (2554,18681) maps to chain index 36 of 78 (rarity: common) logger.js:112:17
[unknown:0] [GRID] Accepted spawn in grid 36 (3/5) logger.js:112:17
[unknown:0] Grid cell (2555,18682) maps to chain index 30 of 78 (rarity: common) logger.js:112:17
[unknown:0] Found base Pokemon for chain 31: geodude (74) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 2 (team average 5 with deviation -3) logger.js:112:17
[unknown:0] Creating new Pokemon geodude with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 2 (common): 6 logger.js:112:17
[unknown:0] Initialized geodude (Lvl 2) with 6 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: geodude logger.js:112:17
[unknown:0] Pokemon family for geodude (chain 31): geodude (Dex #74, Evo level: 25), graveler (Dex #75, Evo level: 50), golem (Dex #76, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdpthpevieo3a07, base_name=geodude, name=geodude, level=2 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: geodude, dex: 74, evolution_level: 25}, {name: graveler, dex: 75, evolution_level: 50}, {name: golem, dex: 76, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=geodude, dex=74, evolution_level=25 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] Starting landuse lookup for geodude at 50.49217547585733,10.856160120227315 logger.js:112:17
[unknown:0] [LANDUSE_LOOKUP] Starting landuse lookup for point 50.49217547585733, 10.856160120227315 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] Checking 23 landuse features for point 50.49217547585733, 10.856160120227315 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] ❌ No valid landuse match found for point 50.49217547585733, 10.856160120227315 logger.js:138:17
[unknown:0] [LANDUSE_DEBUG] Landuse lookup completed for geodude: logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache available: YES logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache features: 23 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Landuse result: null logger.js:112:17
[unknown:0] [LANDUSE_MISS] ⚪ Standard spawn without landuse (normal for random spawns) logger.js:112:17
[unknown:0] Grid cell (2555,18682) maps to chain index 30 of 78 (rarity: common) logger.js:112:17
[unknown:0] [GRID] Rejected spawn in grid 30 - cell full, retrying... logger.js:112:17
[unknown:0] Grid cell (2555,18680) maps to chain index 77 of 78 (rarity: mythical) logger.js:112:17
[unknown:0] Found base Pokemon for chain 78: mew (151) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 5 (exact match to team average) logger.js:112:17
[unknown:0] Creating new Pokemon mew with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized mew (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: mew logger.js:112:17
[unknown:0] Pokemon family for mew (chain 78): mew (Dex #151, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdpthpewvoh7qj1, base_name=mew, name=mew, level=5 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: mew, dex: 151, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=mew, dex=151, evolution_level=none logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] Starting landuse lookup for mew at 50.48900324932292,10.857413587018765 logger.js:112:17
[unknown:0] [LANDUSE_LOOKUP] Starting landuse lookup for point 50.48900324932292, 10.857413587018765 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] Checking 23 landuse features for point 50.48900324932292, 10.857413587018765 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] ❌ No valid landuse match found for point 50.48900324932292, 10.857413587018765 logger.js:138:17
[unknown:0] [LANDUSE_DEBUG] Landuse lookup completed for mew: logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache available: YES logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache features: 23 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Landuse result: null logger.js:112:17
[unknown:0] [LANDUSE_MISS] ⚪ Standard spawn without landuse (normal for random spawns) logger.js:112:17
[unknown:0] Grid cell (2555,18680) maps to chain index 77 of 78 (rarity: mythical) logger.js:112:17
[unknown:0] [GRID] Accepted spawn in grid 77 (1/5) logger.js:112:17
[unknown:0] Grid cell (2555,18682) maps to chain index 30 of 78 (rarity: common) logger.js:112:17
[unknown:0] Found base Pokemon for chain 31: geodude (74) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 1 (team average 5 with deviation -16) logger.js:112:17
[unknown:0] Creating new Pokemon geodude with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Found Pokemon by exact name match: geodude logger.js:112:17
[unknown:0] Pokemon family for geodude (chain 31): geodude (Dex #74, Evo level: 25), graveler (Dex #75, Evo level: 50), golem (Dex #76, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdpthpew9n432la, base_name=geodude, name=geodude, level=1 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: geodude, dex: 74, evolution_level: 25}, {name: graveler, dex: 75, evolution_level: 50}, {name: golem, dex: 76, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=geodude, dex=74, evolution_level=25 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] Starting landuse lookup for geodude at 50.49220845553893,10.856246056059286 logger.js:112:17
[unknown:0] [LANDUSE_LOOKUP] Starting landuse lookup for point 50.49220845553893, 10.856246056059286 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] Checking 23 landuse features for point 50.49220845553893, 10.856246056059286 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] ❌ No valid landuse match found for point 50.49220845553893, 10.856246056059286 logger.js:138:17
[unknown:0] [LANDUSE_DEBUG] Landuse lookup completed for geodude: logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache available: YES logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache features: 23 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Landuse result: null logger.js:112:17
[unknown:0] [LANDUSE_MISS] ⚪ Standard spawn without landuse (normal for random spawns) logger.js:112:17
[unknown:0] Grid cell (2555,18682) maps to chain index 30 of 78 (rarity: common) logger.js:112:17
[unknown:0] [GRID] Rejected spawn in grid 30 - cell full, retrying... logger.js:112:17
[unknown:0] Grid cell (2553,18682) maps to chain index 12 of 78 (rarity: common) logger.js:112:17
[unknown:0] Found base Pokemon for chain 13: nidoran-m (32) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 1 (team average 5 with deviation -7) logger.js:112:17
[unknown:0] Creating new Pokemon nidoran-m with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Found Pokemon by exact name match: nidoran-m logger.js:112:17
[unknown:0] Pokemon family for nidoran-m (chain 13): nidoran-m (Dex #32, Evo level: 16), nidorino (Dex #33, Evo level: 32), nidoking (Dex #34, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdpthpexumyetd9, base_name=nidoran-m, name=nidoran-m, level=1 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: nidoran-m, dex: 32, evolution_level: 16}, {name: nidorino, dex: 33, evolution_level: 32}, {name: nidoking, dex: 34, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=nidoran-m, dex=32, evolution_level=16 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] Starting landuse lookup for nidoran-m at 50.492944452479605,10.849437919537015 logger.js:112:17
[unknown:0] [LANDUSE_LOOKUP] Starting landuse lookup for point 50.492944452479605, 10.849437919537015 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] Checking 23 landuse features for point 50.492944452479605, 10.849437919537015 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] ❌ No valid landuse match found for point 50.492944452479605, 10.849437919537015 logger.js:138:17
[unknown:0] [LANDUSE_DEBUG] Landuse lookup completed for nidoran-m: logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache available: YES logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache features: 23 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Landuse result: null logger.js:112:17
[unknown:0] [LANDUSE_MISS] ⚪ Standard spawn without landuse (normal for random spawns) logger.js:112:17
[unknown:0] Grid cell (2553,18682) maps to chain index 12 of 78 (rarity: common) logger.js:112:17
[unknown:0] [GRID] Accepted spawn in grid 12 (3/5) logger.js:112:17
[unknown:0] Grid cell (2555,18681) maps to chain index 3 of 78 (rarity: common) logger.js:112:17
[unknown:0] Found base Pokemon for chain 4: caterpie (10) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 2 (team average 5 with deviation -3) logger.js:112:17
[unknown:0] Creating new Pokemon caterpie with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 2 (common): 6 logger.js:112:17
[unknown:0] Initialized caterpie (Lvl 2) with 6 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: caterpie logger.js:112:17
[unknown:0] Pokemon family for caterpie (chain 4): caterpie (Dex #10, Evo level: 7), metapod (Dex #11, Evo level: 10), butterfree (Dex #12, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdpthpey9729iz1, base_name=caterpie, name=caterpie, level=2 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: caterpie, dex: 10, evolution_level: 7}, {name: metapod, dex: 11, evolution_level: 10}, {name: butterfree, dex: 12, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=caterpie, dex=10, evolution_level=7 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] Starting landuse lookup for caterpie at 50.49149007863736,10.85802580082439 logger.js:112:17
[unknown:0] [LANDUSE_LOOKUP] Starting landuse lookup for point 50.49149007863736, 10.85802580082439 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] Checking 23 landuse features for point 50.49149007863736, 10.85802580082439 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] ❌ No valid landuse match found for point 50.49149007863736, 10.85802580082439 logger.js:138:17
[unknown:0] [LANDUSE_DEBUG] Landuse lookup completed for caterpie: logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache available: YES logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache features: 23 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Landuse result: null logger.js:112:17
[unknown:0] [LANDUSE_MISS] ⚪ Standard spawn without landuse (normal for random spawns) logger.js:112:17
[unknown:0] Grid cell (2555,18681) maps to chain index 3 of 78 (rarity: common) logger.js:112:17
[unknown:0] [GRID] Accepted spawn in grid 3 (3/5) logger.js:112:17
[unknown:0] Grid distribution summary: 9 cells used, max per cell: 5 logger.js:112:17
[unknown:0]   Grid 30: 5 Pokemon logger.js:112:17
[unknown:0]   Grid 12: 3 Pokemon logger.js:112:17
[unknown:0]   Grid 21: 2 Pokemon logger.js:112:17
[unknown:0]   Grid 28: 1 Pokemon logger.js:112:17
[unknown:0]   Grid 36: 3 Pokemon logger.js:112:17
[unknown:0]   Grid 40: 1 Pokemon logger.js:112:17
[unknown:0]   Grid 3: 3 Pokemon logger.js:112:17
[unknown:0]   Grid 37: 1 Pokemon logger.js:112:17
[unknown:0]   Grid 77: 1 Pokemon logger.js:112:17
[unknown:0] Creating new Pokemon geodude with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 6 (common): 172 logger.js:112:17
[unknown:0] Initialized geodude (Lvl 6) with 172 XP logger.js:112:17
[unknown:0] [CACHE_DEBUG] Applying standard spawn data for geodude: logger.js:112:17
[unknown:0] [CACHE_DEBUG] - pokemonData.landuseData = null logger.js:112:17
[unknown:0] [CACHE_DEBUG] - spawn location = 50.49353930577486,10.857107256433737 logger.js:112:17
[unknown:0] [CACHE_DEBUG] ✗ No landuse data available for geodude - this indicates cache lookup failed logger.js:112:17
[unknown:0] Created final Pokemon: geodude (base: geodude) Level 6, Dex #74 logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon nidoran-m with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized nidoran-m (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] [CACHE_DEBUG] Applying standard spawn data for nidoran-m: logger.js:112:17
[unknown:0] [CACHE_DEBUG] - pokemonData.landuseData = null logger.js:112:17
[unknown:0] [CACHE_DEBUG] - spawn location = 50.49356472751519,10.850000315558779 logger.js:112:17
[unknown:0] [CACHE_DEBUG] ✗ No landuse data available for nidoran-m - this indicates cache lookup failed logger.js:112:17
[unknown:0] Created final Pokemon: nidoran-m (base: nidoran-m) Level 5, Dex #32 logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon meowth with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 6 (common): 172 logger.js:112:17
[unknown:0] Initialized meowth (Lvl 6) with 172 XP logger.js:112:17
[unknown:0] [CACHE_DEBUG] Applying standard spawn data for meowth: logger.js:112:17
[unknown:0] [CACHE_DEBUG] - pokemonData.landuseData = null logger.js:112:17
[unknown:0] [CACHE_DEBUG] - spawn location = 50.492874741188444,10.858863304054294 logger.js:112:17
[unknown:0] [CACHE_DEBUG] ✗ No landuse data available for meowth - this indicates cache lookup failed logger.js:112:17
[unknown:0] Created final Pokemon: meowth (base: meowth) Level 6, Dex #52 logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon bellsprout with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] [CACHE_DEBUG] Applying standard spawn data for bellsprout: logger.js:112:17
[unknown:0] [CACHE_DEBUG] - pokemonData.landuseData = null logger.js:112:17
[unknown:0] [CACHE_DEBUG] - spawn location = 50.4941972117176,10.853798899287353 logger.js:112:17
[unknown:0] [CACHE_DEBUG] ✗ No landuse data available for bellsprout - this indicates cache lookup failed logger.js:112:17
[unknown:0] Created final Pokemon: bellsprout (base: bellsprout) Level 1, Dex #69 logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon meowth with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized meowth (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] [CACHE_DEBUG] Applying standard spawn data for meowth: logger.js:112:17
[unknown:0] [CACHE_DEBUG] - pokemonData.landuseData = null logger.js:112:17
[unknown:0] [CACHE_DEBUG] - spawn location = 50.49226643285825,10.860372373596359 logger.js:112:17
[unknown:0] [CACHE_DEBUG] ✗ No landuse data available for meowth - this indicates cache lookup failed logger.js:112:17
[unknown:0] Created final Pokemon: meowth (base: meowth) Level 5, Dex #52 logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon seel with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 6 (common): 172 logger.js:112:17
[unknown:0] Initialized seel (Lvl 6) with 172 XP logger.js:112:17
[unknown:0] [CACHE_DEBUG] Applying standard spawn data for seel: logger.js:112:17
[unknown:0] [CACHE_DEBUG] - pokemonData.landuseData = null logger.js:112:17
[unknown:0] [CACHE_DEBUG] - spawn location = 50.489469965069105,10.850465878597495 logger.js:112:17
[unknown:0] [CACHE_DEBUG] ✗ No landuse data available for seel - this indicates cache lookup failed logger.js:112:17
[unknown:0] Created final Pokemon: seel (base: seel) Level 6, Dex #86 logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon onix with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 3 (common): 21 logger.js:112:17
[unknown:0] Initialized onix (Lvl 3) with 21 XP logger.js:112:17
[unknown:0] [CACHE_DEBUG] Applying standard spawn data for onix: logger.js:112:17
[unknown:0] [CACHE_DEBUG] - pokemonData.landuseData = null logger.js:112:17
[unknown:0] [CACHE_DEBUG] - spawn location = 50.488347680557574,10.859323440013428 logger.js:112:17
[unknown:0] [CACHE_DEBUG] ✗ No landuse data available for onix - this indicates cache lookup failed logger.js:112:17
[unknown:0] Created final Pokemon: onix (base: onix) Level 3, Dex #95 logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon nidoran-m with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 9 (common): 583 logger.js:112:17
[unknown:0] Initialized nidoran-m (Lvl 9) with 583 XP logger.js:112:17
[unknown:0] [CACHE_DEBUG] Applying standard spawn data for nidoran-m: logger.js:112:17
[unknown:0] [CACHE_DEBUG] - pokemonData.landuseData = null logger.js:112:17
[unknown:0] [CACHE_DEBUG] - spawn location = 50.49354478559857,10.850226321664824 logger.js:112:17
[unknown:0] [CACHE_DEBUG] ✗ No landuse data available for nidoran-m - this indicates cache lookup failed logger.js:112:17
[unknown:0] Created final Pokemon: nidoran-m (base: nidoran-m) Level 9, Dex #32 logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon caterpie with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized caterpie (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] [CACHE_DEBUG] Applying standard spawn data for caterpie: logger.js:112:17
[unknown:0] [CACHE_DEBUG] - pokemonData.landuseData = null logger.js:112:17
[unknown:0] [CACHE_DEBUG] - spawn location = 50.49187817727295,10.858129371384278 logger.js:112:17
[unknown:0] [CACHE_DEBUG] ✗ No landuse data available for caterpie - this indicates cache lookup failed logger.js:112:17
[unknown:0] Created final Pokemon: caterpie (base: caterpie) Level 5, Dex #10 logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon geodude with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized geodude (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] [CACHE_DEBUG] Applying standard spawn data for geodude: logger.js:112:17
[unknown:0] [CACHE_DEBUG] - pokemonData.landuseData = null logger.js:112:17
[unknown:0] [CACHE_DEBUG] - spawn location = 50.49356872046818,10.855479525761162 logger.js:112:17
[unknown:0] [CACHE_DEBUG] ✗ No landuse data available for geodude - this indicates cache lookup failed logger.js:112:17
[unknown:0] Created final Pokemon: geodude (base: geodude) Level 5, Dex #74 logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon caterpie with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] [CACHE_DEBUG] Applying standard spawn data for caterpie: logger.js:112:17
[unknown:0] [CACHE_DEBUG] - pokemonData.landuseData = null logger.js:112:17
[unknown:0] [CACHE_DEBUG] - spawn location = 50.49032041177271,10.856869508882909 logger.js:112:17
[unknown:0] [CACHE_DEBUG] ✗ No landuse data available for caterpie - this indicates cache lookup failed logger.js:112:17
[unknown:0] Created final Pokemon: caterpie (base: caterpie) Level 1, Dex #10 logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon geodude with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized geodude (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] [CACHE_DEBUG] Applying standard spawn data for geodude: logger.js:112:17
[unknown:0] [CACHE_DEBUG] - pokemonData.landuseData = null logger.js:112:17
[unknown:0] [CACHE_DEBUG] - spawn location = 50.49208205892965,10.855085348959538 logger.js:112:17
[unknown:0] [CACHE_DEBUG] ✗ No landuse data available for geodude - this indicates cache lookup failed logger.js:112:17
[unknown:0] Created final Pokemon: geodude (base: geodude) Level 5, Dex #74 logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon grimer with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized grimer (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] [CACHE_DEBUG] Applying standard spawn data for grimer: logger.js:112:17
[unknown:0] [CACHE_DEBUG] - pokemonData.landuseData = null logger.js:112:17
[unknown:0] [CACHE_DEBUG] - spawn location = 50.48872786606624,10.852861114705732 logger.js:112:17
[unknown:0] [CACHE_DEBUG] ✗ No landuse data available for grimer - this indicates cache lookup failed logger.js:112:17
[unknown:0] Created final Pokemon: grimer (base: grimer) Level 5, Dex #88 logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon seel with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized seel (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] [CACHE_DEBUG] Applying standard spawn data for seel: logger.js:112:17
[unknown:0] [CACHE_DEBUG] - pokemonData.landuseData = null logger.js:112:17
[unknown:0] [CACHE_DEBUG] - spawn location = 50.49167376240487,10.852908879805424 logger.js:112:17
[unknown:0] [CACHE_DEBUG] ✗ No landuse data available for seel - this indicates cache lookup failed logger.js:112:17
[unknown:0] Created final Pokemon: seel (base: seel) Level 5, Dex #86 logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon geodude with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 4 (common): 51 logger.js:112:17
[unknown:0] Initialized geodude (Lvl 4) with 51 XP logger.js:112:17
[unknown:0] [CACHE_DEBUG] Applying standard spawn data for geodude: logger.js:112:17
[unknown:0] [CACHE_DEBUG] - pokemonData.landuseData = null logger.js:112:17
[unknown:0] [CACHE_DEBUG] - spawn location = 50.49225119036792,10.857371986338443 logger.js:112:17
[unknown:0] [CACHE_DEBUG] ✗ No landuse data available for geodude - this indicates cache lookup failed logger.js:112:17
[unknown:0] Created final Pokemon: geodude (base: geodude) Level 4, Dex #74 logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon geodude with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 6 (common): 172 logger.js:112:17
[unknown:0] Initialized geodude (Lvl 6) with 172 XP logger.js:112:17
[unknown:0] [CACHE_DEBUG] Applying standard spawn data for geodude: logger.js:112:17
[unknown:0] [CACHE_DEBUG] - pokemonData.landuseData = null logger.js:112:17
[unknown:0] [CACHE_DEBUG] - spawn location = 50.49332532400175,10.85491329091027 logger.js:112:17
[unknown:0] [CACHE_DEBUG] ✗ No landuse data available for geodude - this indicates cache lookup failed logger.js:112:17
[unknown:0] Created final Pokemon: geodude (base: geodude) Level 6, Dex #74 logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon seel with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 14 (common): 2195 logger.js:112:17
[unknown:0] Initialized seel (Lvl 14) with 2195 XP logger.js:112:17
[unknown:0] [CACHE_DEBUG] Applying standard spawn data for seel: logger.js:112:17
[unknown:0] [CACHE_DEBUG] - pokemonData.landuseData = null logger.js:112:17
[unknown:0] [CACHE_DEBUG] - spawn location = 50.49002087693394,10.853792179901614 logger.js:112:17
[unknown:0] [CACHE_DEBUG] ✗ No landuse data available for seel - this indicates cache lookup failed logger.js:112:17
[unknown:0] Created final Pokemon: seel (base: seel) Level 14, Dex #86 logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon mew with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized mew (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] [CACHE_DEBUG] Applying standard spawn data for mew: logger.js:112:17
[unknown:0] [CACHE_DEBUG] - pokemonData.landuseData = null logger.js:112:17
[unknown:0] [CACHE_DEBUG] - spawn location = 50.48900324932292,10.857413587018765 logger.js:112:17
[unknown:0] [CACHE_DEBUG] ✗ No landuse data available for mew - this indicates cache lookup failed logger.js:112:17
[unknown:0] Created final Pokemon: mew (base: mew) Level 5, Dex #151 logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon nidoran-m with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] [CACHE_DEBUG] Applying standard spawn data for nidoran-m: logger.js:112:17
[unknown:0] [CACHE_DEBUG] - pokemonData.landuseData = null logger.js:112:17
[unknown:0] [CACHE_DEBUG] - spawn location = 50.492944452479605,10.849437919537015 logger.js:112:17
[unknown:0] [CACHE_DEBUG] ✗ No landuse data available for nidoran-m - this indicates cache lookup failed logger.js:112:17
[unknown:0] Created final Pokemon: nidoran-m (base: nidoran-m) Level 1, Dex #32 logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon caterpie with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 2 (common): 6 logger.js:112:17
[unknown:0] Initialized caterpie (Lvl 2) with 6 XP logger.js:112:17
[unknown:0] [CACHE_DEBUG] Applying standard spawn data for caterpie: logger.js:112:17
[unknown:0] [CACHE_DEBUG] - pokemonData.landuseData = null logger.js:112:17
[unknown:0] [CACHE_DEBUG] - spawn location = 50.49149007863736,10.85802580082439 logger.js:112:17
[unknown:0] [CACHE_DEBUG] ✗ No landuse data available for caterpie - this indicates cache lookup failed logger.js:112:17
[unknown:0] Created final Pokemon: caterpie (base: caterpie) Level 2, Dex #10 logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] [LANDUSE_STATS] 📊 Standard spawn results with 1km landuse coverage: logger.js:138:17
[unknown:0] [LANDUSE_STATS] 🎯 Total spawn attempts: 22 logger.js:138:17
[unknown:0] [LANDUSE_STATS] ✅ Successful landuse spawns: 0/20 (0%) logger.js:138:17
[unknown:0] [LANDUSE_STATS] ⚪ Random spawns without landuse: 22/20 (110%) logger.js:138:17
[unknown:0] [LANDUSE_STATS] ⚡ Average attempts per spawn: 1.1 logger.js:138:17
[unknown:0] [LANDUSE_STATS] 🗺️ Coverage area: 1km radius (3.141592653589793 km²) logger.js:138:17
[unknown:0] [STANDARD_SPAWN] ✅ Successfully created 20 standard Pokemon with comprehensive landuse coverage logger.js:138:17
[unknown:0] [CACHE_DEBUG] Spawned 20 standard + 20 landuse Pokemon logger.js:112:17
[unknown:0] Re-enabled storage sync after reset logger.js:112:17
[unknown:0] Spawn reset complete. New Pokemon count: 40 logger.js:112:17
[unknown:0] Browser Geolocation update: lat=50.4922112, lng=10.8560384, accuracy=1219.9206785921297 logger.js:112:17
[unknown:0] Updating player sprite at 50.4922112, 10.8560384, heading: 0 logger.js:112:17
[unknown:0] Player is static, distSinceLastMove: 0 logger.js:112:17
[unknown:0] Browser Geolocation update: lat=50.4922112, lng=10.8560384, accuracy=1219.9206785921297 logger.js:112:17
[unknown:0] Updating player sprite at 50.4922112, 10.8560384, heading: 0 logger.js:112:17
[unknown:0] Player is static, distSinceLastMove: 0 logger.js:112:17
[unknown:0] Browser Geolocation update: lat=50.4922112, lng=10.8560384, accuracy=1219.9206785921297 logger.js:112:17
[unknown:0] Updating player sprite at 50.4922112, 10.8560384, heading: 0 logger.js:112:17
[unknown:0] Player is static, distSinceLastMove: 0 logger.js:112:17
[unknown:0] Browser Geolocation update: lat=50.4922112, lng=10.8560384, accuracy=1219.9206785921297 logger.js:112:17
[unknown:0] Updating player sprite at 50.4922112, 10.8560384, heading: 0 logger.js:112:17
[unknown:0] Player is static, distSinceLastMove: 0 logger.js:112:17
MouseEvent.mozPressure sollte nicht mehr verwendet werden. Verwenden Sie PointerEvent.pressure stattdessen. leaflet.js:5:320
MouseEvent.mozInputSource sollte nicht mehr verwendet werden. Verwenden Sie PointerEvent.pointerType stattdessen. leaflet.js:5:320
[unknown:0] [POKEMON_DEBUG] {"id":"mdpthpf2szcpufr","base_name":"geodude","name":"geodude","level":5,"dex_number":74,"types":["rock","ground"],"landuse":{"special":false,"type":null,"typeName":null,"featureId":null},"currentForm":{}} logger.js:112:17
[unknown:0] [LANDUSE_INFO] Pokemon geodude: special=false, type=null, typeName=null logger.js:112:17
[unknown:0] Display form for geodude: {"name":"geodude","sprite":"./src/PokemonSprites/74.png","dex_number":74,"types":["rock","ground"],"evolution_chain_id":31} logger.js:112:17
[unknown:0] [POKEMON_DEBUG] {"id":"mdpthpf2v1ckumf","base_name":"geodude","name":"geodude","level":4,"dex_number":74,"types":["rock","ground"],"landuse":{"special":false,"type":null,"typeName":null,"featureId":null},"currentForm":{}} logger.js:112:17
[unknown:0] [LANDUSE_INFO] Pokemon geodude: special=false, type=null, typeName=null logger.js:112:17
[unknown:0] Display form for geodude: {"name":"geodude","sprite":"./src/PokemonSprites/74.png","dex_number":74,"types":["rock","ground"],"evolution_chain_id":31} logger.js:112:17
[unknown:0] [POKEMON_DEBUG] {"id":"mdpthpf1zpww49g","base_name":"caterpie","name":"caterpie","level":5,"dex_number":10,"types":["bug"],"landuse":{"special":false,"type":null,"typeName":null,"featureId":null},"currentForm":{}} logger.js:112:17
[unknown:0] [LANDUSE_INFO] Pokemon caterpie: special=false, type=null, typeName=null logger.js:112:17
[unknown:0] Display form for caterpie: {"name":"caterpie","sprite":"./src/PokemonSprites/10.png","dex_number":10,"types":["bug"],"evolution_chain_id":4} logger.js:112:17
[unknown:0] [POKEMON_DEBUG] {"id":"mdpthpf1ddp02ku","base_name":"meowth","name":"meowth","level":5,"dex_number":52,"types":["normal"],"landuse":{"special":false,"type":null,"typeName":null,"featureId":null},"currentForm":{}} logger.js:112:17
[unknown:0] [LANDUSE_INFO] Pokemon meowth: special=false, type=null, typeName=null logger.js:112:17
[unknown:0] Display form for meowth: {"name":"meowth","sprite":"./src/PokemonSprites/52.png","dex_number":52,"types":["normal"],"evolution_chain_id":22} logger.js:112:17
[unknown:0] Browser Geolocation update: lat=50.4922112, lng=10.8560384, accuracy=1219.9206785921297 logger.js:112:17
[unknown:0] Updating player sprite at 50.4922112, 10.8560384, heading: 0 logger.js:112:17
[unknown:0] Player is static, distSinceLastMove: 0 logger.js:112:17
Source-Map-Fehler: Error: NetworkError when attempting to fetch resource.
Ressourcen-Adresse: https://unpkg.com/leaflet/dist/leaflet.js
Source-Map-Adresse: leaflet.js.map
[unknown:0] Browser Geolocation update: lat=50.4922112, lng=10.8560384, accuracy=1219.9206785921297 logger.js:112:17
[unknown:0] Updating player sprite at 50.4922112, 10.8560384, heading: 0 logger.js:112:17
[unknown:0] Player is static, distSinceLastMove: 0 logger.js:112:17
[unknown:0] Browser Geolocation update: lat=50.4922112, lng=10.8560384, accuracy=1219.9206785921297 logger.js:112:17
[unknown:0] Updating player sprite at 50.4922112, 10.8560384, heading: 0 logger.js:112:17
[unknown:0] Player is static, distSinceLastMove: 0 logger.js:112:17
[unknown:0] Browser Geolocation update: lat=50.4922112, lng=10.8560384, accuracy=1219.9206785921297 logger.js:112:17
[unknown:0] Updating player sprite at 50.4922112, 10.8560384, heading: 0 logger.js:112:17
[unknown:0] Player is static, distSinceLastMove: 0 logger.js:112:17
[unknown:0] Browser Geolocation update: lat=50.4922112, lng=10.8560384, accuracy=1219.9206785921297 logger.js:112:17
[unknown:0] Updating player sprite at 50.4922112, 10.8560384, heading: 0 logger.js:112:17
[unknown:0] Player is static, distSinceLastMove: 0 logger.js:112:17
[unknown:0] Browser Geolocation update: lat=50.4922112, lng=10.8560384, accuracy=1219.9206785921297 logger.js:112:17
[unknown:0] Updating player sprite at 50.4922112, 10.8560384, heading: 0 logger.js:112:17
[unknown:0] Player is static, distSinceLastMove: 0 logger.js:112:17
[unknown:0] Browser Geolocation update: lat=50.4922112, lng=10.8560384, accuracy=1219.9206785921297 logger.js:112:17
[unknown:0] Updating player sprite at 50.4922112, 10.8560384, heading: 0 logger.js:112:17
[unknown:0] Player is static, distSinceLastMove: 0 logger.js:112:17
[unknown:0] Browser Geolocation update: lat=50.4922112, lng=10.8560384, accuracy=1219.9206785921297 logger.js:112:17
[unknown:0] Updating player sprite at 50.4922112, 10.8560384, heading: 0 logger.js:112:17
[unknown:0] Player is static, distSinceLastMove: 0 logger.js:112:17
[unknown:0] Browser Geolocation update: lat=50.4922112, lng=10.8560384, accuracy=1219.9206785921297 logger.js:112:17
[unknown:0] Updating player sprite at 50.4922112, 10.8560384, heading: 0 logger.js:112:17
[unknown:0] Player is static, distSinceLastMove: 0 logger.js:112:17
[unknown:0] Browser Geolocation update: lat=50.4922112, lng=10.8560384, accuracy=1219.9206785921297 logger.js:112:17
[unknown:0] Updating player sprite at 50.4922112, 10.8560384, heading: 0 logger.js:112:17
[unknown:0] Player is static, distSinceLastMove: 0 logger.js:112:17
[unknown:0] Browser Geolocation update: lat=50.4922112, lng=10.8560384, accuracy=1219.9206785921297 logger.js:112:17
[unknown:0] Updating player sprite at 50.4922112, 10.8560384, heading: 0 logger.js:112:17
[unknown:0] Player is static, distSinceLastMove: 0 logger.js:112:17
[unknown:0] Browser Geolocation update: lat=50.4922112, lng=10.8560384, accuracy=1219.9206785921297 logger.js:112:17
[unknown:0] Updating player sprite at 50.4922112, 10.8560384, heading: 0 logger.js:112:17
[unknown:0] Player is static, distSinceLastMove: 0 logger.js:112:17
[unknown:0] Debug mode toggled: false logger.js:112:17
[unknown:0] Grid cell (2551,18679) maps to chain index 2 of 78 (rarity: starter) logger.js:112:17
[unknown:0] Grid cell (2552,18679) maps to chain index 25 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2553,18679) maps to chain index 26 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2554,18679) maps to chain index 61 of 78 (rarity: scarce) logger.js:112:17
[unknown:0] Grid cell (2555,18679) maps to chain index 10 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2556,18679) maps to chain index 46 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2557,18679) maps to chain index 18 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2558,18679) maps to chain index 24 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2551,18680) maps to chain index 57 of 78 (rarity: scarce) logger.js:112:17
[unknown:0] Grid cell (2552,18680) maps to chain index 36 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2553,18680) maps to chain index 1 of 78 (rarity: starter) logger.js:112:17
[unknown:0] Grid cell (2554,18680) maps to chain index 37 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2555,18680) maps to chain index 77 of 78 (rarity: mythical) logger.js:112:17
[unknown:0] Grid cell (2556,18680) maps to chain index 40 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2557,18680) maps to chain index 56 of 78 (rarity: rare) logger.js:112:17
[unknown:0] Grid cell (2558,18680) maps to chain index 63 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2551,18681) maps to chain index 43 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2552,18681) maps to chain index 20 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2553,18681) maps to chain index 7 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2554,18681) maps to chain index 36 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2555,18681) maps to chain index 3 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2556,18681) maps to chain index 45 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2557,18681) maps to chain index 65 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2558,18681) maps to chain index 23 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2551,18682) maps to chain index 56 of 78 (rarity: rare) logger.js:112:17
[unknown:0] Grid cell (2552,18682) maps to chain index 21 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2553,18682) maps to chain index 12 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2554,18682) maps to chain index 28 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2555,18682) maps to chain index 30 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2556,18682) maps to chain index 21 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2557,18682) maps to chain index 23 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2558,18682) maps to chain index 56 of 78 (rarity: rare) logger.js:112:17
[unknown:0] Grid cell (2551,18683) maps to chain index 41 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2552,18683) maps to chain index 33 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2553,18683) maps to chain index 36 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2554,18683) maps to chain index 28 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2555,18683) maps to chain index 22 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2556,18683) maps to chain index 35 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2557,18683) maps to chain index 10 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2558,18683) maps to chain index 43 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2551,18684) maps to chain index 22 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2552,18684) maps to chain index 56 of 78 (rarity: rare) logger.js:112:17
[unknown:0] Grid cell (2553,18684) maps to chain index 25 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2554,18684) maps to chain index 60 of 78 (rarity: scarce) logger.js:112:17
[unknown:0] Grid cell (2555,18684) maps to chain index 52 of 78 (rarity: scarce) logger.js:112:17
[unknown:0] Grid cell (2556,18684) maps to chain index 33 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2557,18684) maps to chain index 18 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2558,18684) maps to chain index 4 of 78 (rarity: common) logger.js:112:17
[unknown:0] Debug mode toggled: true logger.js:112:17
[unknown:0] Browser Geolocation update: lat=50.4922112, lng=10.8560384, accuracy=1219.9206785921297 logger.js:112:17
[unknown:0] Updating player sprite at 50.4922112, 10.8560384, heading: 0 logger.js:112:17
[unknown:0] Player is static, distSinceLastMove: 0 logger.js:112:17
[unknown:0] Debug mode toggled: false logger.js:112:17
[unknown:0] Grid cell (2551,18679) maps to chain index 2 of 78 (rarity: starter) logger.js:112:17
[unknown:0] Grid cell (2552,18679) maps to chain index 25 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2553,18679) maps to chain index 26 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2554,18679) maps to chain index 61 of 78 (rarity: scarce) logger.js:112:17
[unknown:0] Grid cell (2555,18679) maps to chain index 10 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2556,18679) maps to chain index 46 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2557,18679) maps to chain index 18 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2558,18679) maps to chain index 24 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2551,18680) maps to chain index 57 of 78 (rarity: scarce) logger.js:112:17
[unknown:0] Grid cell (2552,18680) maps to chain index 36 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2553,18680) maps to chain index 1 of 78 (rarity: starter) logger.js:112:17
[unknown:0] Grid cell (2554,18680) maps to chain index 37 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2555,18680) maps to chain index 77 of 78 (rarity: mythical) logger.js:112:17
[unknown:0] Grid cell (2556,18680) maps to chain index 40 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2557,18680) maps to chain index 56 of 78 (rarity: rare) logger.js:112:17
[unknown:0] Grid cell (2558,18680) maps to chain index 63 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2551,18681) maps to chain index 43 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2552,18681) maps to chain index 20 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2553,18681) maps to chain index 7 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2554,18681) maps to chain index 36 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2555,18681) maps to chain index 3 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2556,18681) maps to chain index 45 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2557,18681) maps to chain index 65 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2558,18681) maps to chain index 23 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2551,18682) maps to chain index 56 of 78 (rarity: rare) logger.js:112:17
[unknown:0] Grid cell (2552,18682) maps to chain index 21 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2553,18682) maps to chain index 12 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2554,18682) maps to chain index 28 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2555,18682) maps to chain index 30 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2556,18682) maps to chain index 21 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2557,18682) maps to chain index 23 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2558,18682) maps to chain index 56 of 78 (rarity: rare) logger.js:112:17
[unknown:0] Grid cell (2551,18683) maps to chain index 41 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2552,18683) maps to chain index 33 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2553,18683) maps to chain index 36 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2554,18683) maps to chain index 28 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2555,18683) maps to chain index 22 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2556,18683) maps to chain index 35 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2557,18683) maps to chain index 10 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2558,18683) maps to chain index 43 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2551,18684) maps to chain index 22 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2552,18684) maps to chain index 56 of 78 (rarity: rare) logger.js:112:17
[unknown:0] Grid cell (2553,18684) maps to chain index 25 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2554,18684) maps to chain index 60 of 78 (rarity: scarce) logger.js:112:17
[unknown:0] Grid cell (2555,18684) maps to chain index 52 of 78 (rarity: scarce) logger.js:112:17
[unknown:0] Grid cell (2556,18684) maps to chain index 33 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2557,18684) maps to chain index 18 of 78 (rarity: common) logger.js:112:17
[unknown:0] Grid cell (2558,18684) maps to chain index 4 of 78 (rarity: common) logger.js:112:17
[unknown:0] Debug mode toggled: true logger.js:112:17
[unknown:0] Starting spawn reset process... logger.js:112:17
[unknown:0] Clearing Pokemon spawns from storage logger.js:112:17
[unknown:0] Removed from storage: timeEventSpawns logger.js:112:17
[unknown:0] Cleared spawns from storage: success logger.js:112:17
[unknown:0] Reset spawn flags: lastSpawnLatLng=null, loadedFromStorage=false logger.js:112:17
[unknown:0] Disabled storage sync during reset logger.js:112:17
[unknown:0] Spawning new Pokemon at location: 50.4922112, 10.8560384 logger.js:112:17
[unknown:0] [CACHE_DEBUG] Pre-loading landuse cache before spawning... logger.js:112:17
[unknown:0] Loading landuse data for area: 50492:10856:500 logger.js:112:17
[OVERPASS-CACHE] Cache hit for key: 50.492,10.856,500 overpass-landuse.js:21:13
[unknown:0] Cached landuse data with 3 features for area: 50492:10856:500 logger.js:112:17
[unknown:0] [CACHE_DEBUG] Pre-loaded cache with 3 features logger.js:112:17
[unknown:0] [STANDARD_SPAWN] 🎯 Starting spawnRandomPokemons with count=20 at 50.4922112,10.8560384 logger.js:138:17
[unknown:0] Created pokedex snapshot with 151 entries logger.js:112:17
[unknown:0] [LANDUSE_CACHE] 🗺️ Loading comprehensive landuse cache for 1km area: 50.4922112, 10.8560384 logger.js:138:17
[unknown:0] [LANDUSE_CACHE] 📡 Fetching ways + relations for landuse/natural/leisure from Overpass API... logger.js:138:17
[unknown:0] Loading landuse data for area: 50492:10856:1000 logger.js:112:17
[OVERPASS-CACHE] Cache hit for key: 50.492,10.856,1000 overpass-landuse.js:21:13
[unknown:0] [LANDUSE_SPAWN] 🏞️ Starting spawnLanduseSpecialPokemons with count=20 at 50.4922112,10.8560384 logger.js:138:17
[unknown:0] Created pokedex snapshot for landuse spawning with 151 entries logger.js:112:17
[unknown:0] [LANDUSE_SPECIAL] 🗺️ Loading 1km landuse cache for special spawning: 50.4922112, 10.8560384 logger.js:112:17
[unknown:0] Loading landuse data for area: 50492:10856:1000 logger.js:112:17
[OVERPASS-CACHE] Cache hit for key: 50.492,10.856,1000 overpass-landuse.js:21:13
[unknown:0] Cached landuse data with 23 features for area: 50492:10856:1000 2 logger.js:112:17
[unknown:0] [LANDUSE_CACHE] ✅ Landuse cache loaded with 23 total features logger.js:138:17
[unknown:0] [LANDUSE_CACHE] 📊 Feature analysis: 23/23 valid features logger.js:138:17
[unknown:0] [LANDUSE_CACHE] 🏷️ Feature types: residential(5), cemetery(2), allotments(1), industrial(2), wood(5), grass(1), playground(1), pitch(1), meadow(4), cliff(1) logger.js:138:17
[unknown:0] [STANDARD_SPAWN] 🎯 Starting intelligent spawn collection for 20 Pokemon with landuse optimization... logger.js:138:17
[unknown:0] Starting parallel landuse data collection for 20 Pokemon with grid distribution... logger.js:112:17
[unknown:0] Grid cell (2556,18682) maps to chain index 21 of 78 (rarity: common) logger.js:112:17
[unknown:0] Found base Pokemon for chain 22: meowth (52) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 4 (team average 5 with deviation -1) logger.js:112:17
[unknown:0] Creating new Pokemon meowth with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 4 (common): 51 logger.js:112:17
[unknown:0] Initialized meowth (Lvl 4) with 51 XP logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 3 (team average 5 with deviation -2) logger.js:112:17
[unknown:0] Creating new Pokemon magneton with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 3 (common): 21 logger.js:112:17
[unknown:0] Initialized magneton (Lvl 3) with 21 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: meowth logger.js:112:17
[unknown:0] Pokemon family for meowth (chain 22): meowth (Dex #52, Evo level: 28), persian (Dex #53, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdptjiqn0f7v6vw, base_name=meowth, name=meowth, level=4 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: meowth, dex: 52, evolution_level: 28}, {name: persian, dex: 53, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=meowth, dex=52, evolution_level=28 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] Starting landuse lookup for meowth at 50.49412288826295,10.860413051208836 logger.js:112:17
[unknown:0] [LANDUSE_LOOKUP] Starting landuse lookup for point 50.49412288826295, 10.860413051208836 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] Checking 23 landuse features for point 50.49412288826295, 10.860413051208836 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] ✅ Found valid landuse match: residential for point 50.49412288826295, 10.860413051208836 logger.js:138:17
[unknown:0] [LANDUSE_DEBUG] Landuse lookup completed for meowth: logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache available: YES logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache features: 23 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Landuse result: residential logger.js:112:17
[unknown:0] [LANDUSE_SUCCESS] ✅ Standard spawn with landuse: residential logger.js:112:17
[unknown:0] Grid cell (2556,18682) maps to chain index 21 of 78 (rarity: common) logger.js:112:17
[unknown:0] [GRID] Accepted spawn in grid 21 (1/5) logger.js:112:17
[unknown:0] Grid cell (2554,18681) maps to chain index 36 of 78 (rarity: common) logger.js:112:17
[unknown:0] Found base Pokemon for chain 37: seel (86) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 2 (team average 5 with deviation -3) logger.js:112:17
[unknown:0] Creating new Pokemon seel with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 2 (common): 6 logger.js:112:17
[unknown:0] Initialized seel (Lvl 2) with 6 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: magneton logger.js:112:17
[unknown:0] Pokemon family for magneton (chain 34): magnemite (Dex #81, Evo level: 30), magneton (Dex #82, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdptjiqnytzl3qu, base_name=magneton, name=magneton, level=3 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: magnemite, dex: 81, evolution_level: 30}, {name: magneton, dex: 82, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=magnemite, dex=81, evolution_level=30 logger.js:112:17
[unknown:0] Grid cell (2554,18679) maps to chain index 61 of 78 (rarity: scarce) logger.js:112:17
[unknown:0] Accepted landuse spawn in grid 61 (1/5) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 2 (team average 5 with deviation -3) logger.js:112:17
[unknown:0] Creating new Pokemon magneton with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 2 (common): 6 logger.js:112:17
[unknown:0] Initialized magneton (Lvl 2) with 6 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: seel logger.js:112:17
[unknown:0] Pokemon family for seel (chain 37): seel (Dex #86, Evo level: 34), dewgong (Dex #87, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdptjiqp8v9jjsk, base_name=seel, name=seel, level=2 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: seel, dex: 86, evolution_level: 34}, {name: dewgong, dex: 87, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=seel, dex=86, evolution_level=34 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] Starting landuse lookup for seel at 50.49174875304111,10.854156284031964 logger.js:112:17
[unknown:0] [LANDUSE_LOOKUP] Starting landuse lookup for point 50.49174875304111, 10.854156284031964 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] Checking 23 landuse features for point 50.49174875304111, 10.854156284031964 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] ❌ No valid landuse match found for point 50.49174875304111, 10.854156284031964 logger.js:138:17
[unknown:0] [LANDUSE_DEBUG] Landuse lookup completed for seel: logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache available: YES logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache features: 23 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Landuse result: null logger.js:112:17
[unknown:0] [LANDUSE_MISS] ⚪ Standard spawn without landuse (normal for random spawns) logger.js:112:17
[unknown:0] Grid cell (2554,18681) maps to chain index 36 of 78 (rarity: common) logger.js:112:17
[unknown:0] [GRID] Accepted spawn in grid 36 (1/5) logger.js:112:17
[unknown:0] Grid cell (2554,18682) maps to chain index 28 of 78 (rarity: common) logger.js:112:17
[unknown:0] Found base Pokemon for chain 29: bellsprout (69) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 7 (team average 5 with deviation 2) logger.js:112:17
[unknown:0] Creating new Pokemon bellsprout with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 7 (common): 274 logger.js:112:17
[unknown:0] Initialized bellsprout (Lvl 7) with 274 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: magneton logger.js:112:17
[unknown:0] Pokemon family for magneton (chain 34): magnemite (Dex #81, Evo level: 30), magneton (Dex #82, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdptjiqqsumzqb0, base_name=magneton, name=magneton, level=2 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: magnemite, dex: 81, evolution_level: 30}, {name: magneton, dex: 82, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=magnemite, dex=81, evolution_level=30 logger.js:112:17
[unknown:0] Grid cell (2554,18679) maps to chain index 61 of 78 (rarity: scarce) logger.js:112:17
[unknown:0] Accepted landuse spawn in grid 61 (2/5) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 1 (team average 5 with deviation -4) logger.js:112:17
[unknown:0] Creating new Pokemon tauros with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Found Pokemon by exact name match: bellsprout logger.js:112:17
[unknown:0] Pokemon family for bellsprout (chain 29): bellsprout (Dex #69, Evo level: 21), weepinbell (Dex #70, Evo level: 42), victreebel (Dex #71, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdptjiqqw021hu9, base_name=bellsprout, name=bellsprout, level=7 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: bellsprout, dex: 69, evolution_level: 21}, {name: weepinbell, dex: 70, evolution_level: 42}, {name: victreebel, dex: 71, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=bellsprout, dex=69, evolution_level=21 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] Starting landuse lookup for bellsprout at 50.494157711384396,10.852399739033908 logger.js:112:17
[unknown:0] [LANDUSE_LOOKUP] Starting landuse lookup for point 50.494157711384396, 10.852399739033908 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] Checking 23 landuse features for point 50.494157711384396, 10.852399739033908 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] ❌ No valid landuse match found for point 50.494157711384396, 10.852399739033908 logger.js:138:17
[unknown:0] [LANDUSE_DEBUG] Landuse lookup completed for bellsprout: logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache available: YES logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache features: 23 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Landuse result: null logger.js:112:17
[unknown:0] [LANDUSE_MISS] ⚪ Standard spawn without landuse (normal for random spawns) logger.js:112:17
[unknown:0] Grid cell (2554,18682) maps to chain index 28 of 78 (rarity: common) logger.js:112:17
[unknown:0] [GRID] Accepted spawn in grid 28 (1/5) logger.js:112:17
[unknown:0] Grid cell (2555,18682) maps to chain index 30 of 78 (rarity: common) logger.js:112:17
[unknown:0] Found base Pokemon for chain 31: geodude (74) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 5 (exact match to team average) logger.js:112:17
[unknown:0] Creating new Pokemon geodude with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized geodude (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: tauros logger.js:112:17
[unknown:0] Pokemon family for tauros (chain 63): tauros (Dex #128, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdptjiqrlcyiegy, base_name=tauros, name=tauros, level=1 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: tauros, dex: 128, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=tauros, dex=128, evolution_level=none logger.js:112:17
[unknown:0] Grid cell (2553,18680) maps to chain index 1 of 78 (rarity: starter) logger.js:112:17
[unknown:0] Accepted landuse spawn in grid 1 (1/5) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 1 (team average 5 with deviation -15) logger.js:112:17
[unknown:0] Creating new Pokemon pidgeot with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Found Pokemon by exact name match: geodude logger.js:112:17
[unknown:0] Pokemon family for geodude (chain 31): geodude (Dex #74, Evo level: 25), graveler (Dex #75, Evo level: 50), golem (Dex #76, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdptjiqt1ubqf3v, base_name=geodude, name=geodude, level=5 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: geodude, dex: 74, evolution_level: 25}, {name: graveler, dex: 75, evolution_level: 50}, {name: golem, dex: 76, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=geodude, dex=74, evolution_level=25 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] Starting landuse lookup for geodude at 50.49414283667046,10.855925476484767 logger.js:112:17
[unknown:0] [LANDUSE_LOOKUP] Starting landuse lookup for point 50.49414283667046, 10.855925476484767 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] Checking 23 landuse features for point 50.49414283667046, 10.855925476484767 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] ❌ No valid landuse match found for point 50.49414283667046, 10.855925476484767 logger.js:138:17
[unknown:0] [LANDUSE_DEBUG] Landuse lookup completed for geodude: logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache available: YES logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache features: 23 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Landuse result: null logger.js:112:17
[unknown:0] [LANDUSE_MISS] ⚪ Standard spawn without landuse (normal for random spawns) logger.js:112:17
[unknown:0] Grid cell (2555,18682) maps to chain index 30 of 78 (rarity: common) logger.js:112:17
[unknown:0] [GRID] Accepted spawn in grid 30 (1/5) logger.js:112:17
[unknown:0] Grid cell (2555,18681) maps to chain index 3 of 78 (rarity: common) logger.js:112:17
[unknown:0] Found base Pokemon for chain 4: caterpie (10) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 8 (team average 5 with deviation 3) logger.js:112:17
[unknown:0] Creating new Pokemon caterpie with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 8 (common): 409 logger.js:112:17
[unknown:0] Initialized caterpie (Lvl 8) with 409 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: pidgeot logger.js:112:17
[unknown:0] Pokemon family for pidgeot (chain 6): pidgey (Dex #16, Evo level: 18), pidgeotto (Dex #17, Evo level: 36), pidgeot (Dex #18, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdptjiqu57g00nv, base_name=pidgeot, name=pidgeot, level=1 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: pidgey, dex: 16, evolution_level: 18}, {name: pidgeotto, dex: 17, evolution_level: 36}, {name: pidgeot, dex: 18, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=pidgey, dex=16, evolution_level=18 logger.js:112:17
[unknown:0] Grid cell (2557,18680) maps to chain index 56 of 78 (rarity: rare) logger.js:112:17
[unknown:0] Accepted landuse spawn in grid 56 (1/5) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 5 (exact match to team average) logger.js:112:17
[unknown:0] Creating new Pokemon snorlax with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized snorlax (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: caterpie logger.js:112:17
[unknown:0] Pokemon family for caterpie (chain 4): caterpie (Dex #10, Evo level: 7), metapod (Dex #11, Evo level: 10), butterfree (Dex #12, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdptjiquf5rftpw, base_name=caterpie, name=caterpie, level=8 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: caterpie, dex: 10, evolution_level: 7}, {name: metapod, dex: 11, evolution_level: 10}, {name: butterfree, dex: 12, evolution_level: none} logger.js:112:17
[unknown:0] Evolution by level: caterpie -> metapod (Level: 8 >= 7) logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=metapod, dex=11, evolution_level=10 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] Starting landuse lookup for caterpie at 50.49104178413201,10.855307665272447 logger.js:112:17
[unknown:0] [LANDUSE_LOOKUP] Starting landuse lookup for point 50.49104178413201, 10.855307665272447 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] Checking 23 landuse features for point 50.49104178413201, 10.855307665272447 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] ❌ No valid landuse match found for point 50.49104178413201, 10.855307665272447 logger.js:138:17
[unknown:0] [LANDUSE_DEBUG] Landuse lookup completed for caterpie: logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache available: YES logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache features: 23 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Landuse result: null logger.js:112:17
[unknown:0] [LANDUSE_MISS] ⚪ Standard spawn without landuse (normal for random spawns) logger.js:112:17
[unknown:0] Grid cell (2555,18681) maps to chain index 3 of 78 (rarity: common) logger.js:112:17
[unknown:0] [GRID] Accepted spawn in grid 3 (1/5) logger.js:112:17
[unknown:0] Grid cell (2555,18682) maps to chain index 30 of 78 (rarity: common) logger.js:112:17
[unknown:0] Found base Pokemon for chain 31: geodude (74) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 5 (exact match to team average) logger.js:112:17
[unknown:0] Creating new Pokemon geodude with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized geodude (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: snorlax logger.js:112:17
[unknown:0] Pokemon family for snorlax (chain 72): snorlax (Dex #143, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdptjiqvjvzfjkq, base_name=snorlax, name=snorlax, level=5 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: snorlax, dex: 143, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=snorlax, dex=143, evolution_level=none logger.js:112:17
[unknown:0] Grid cell (2557,18680) maps to chain index 56 of 78 (rarity: rare) logger.js:112:17
[unknown:0] Accepted landuse spawn in grid 56 (2/5) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 5 (exact match to team average) logger.js:112:17
[unknown:0] Creating new Pokemon eevee with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized eevee (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: geodude logger.js:112:17
[unknown:0] Pokemon family for geodude (chain 31): geodude (Dex #74, Evo level: 25), graveler (Dex #75, Evo level: 50), golem (Dex #76, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdptjiqw294zmf4, base_name=geodude, name=geodude, level=5 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: geodude, dex: 74, evolution_level: 25}, {name: graveler, dex: 75, evolution_level: 50}, {name: golem, dex: 76, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=geodude, dex=74, evolution_level=25 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] Starting landuse lookup for geodude at 50.49231054474602,10.8554706066709 logger.js:112:17
[unknown:0] [LANDUSE_LOOKUP] Starting landuse lookup for point 50.49231054474602, 10.8554706066709 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] Checking 23 landuse features for point 50.49231054474602, 10.8554706066709 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] ❌ No valid landuse match found for point 50.49231054474602, 10.8554706066709 logger.js:138:17
[unknown:0] [LANDUSE_DEBUG] Landuse lookup completed for geodude: logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache available: YES logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache features: 23 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Landuse result: null logger.js:112:17
[unknown:0] [LANDUSE_MISS] ⚪ Standard spawn without landuse (normal for random spawns) logger.js:112:17
[unknown:0] Grid cell (2555,18682) maps to chain index 30 of 78 (rarity: common) logger.js:112:17
[unknown:0] [GRID] Accepted spawn in grid 30 (2/5) logger.js:112:17
[unknown:0] Grid cell (2555,18683) maps to chain index 22 of 78 (rarity: common) logger.js:112:17
[unknown:0] Found base Pokemon for chain 23: psyduck (54) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 4 (team average 5 with deviation -1) logger.js:112:17
[unknown:0] Creating new Pokemon psyduck with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 4 (common): 51 logger.js:112:17
[unknown:0] Initialized psyduck (Lvl 4) with 51 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: eevee logger.js:112:17
[unknown:0] Pokemon family for eevee (chain 67): eevee (Dex #133, Evo level: null), vaporeon (Dex #134, Evo level: null), jolteon (Dex #135, Evo level: null), flareon (Dex #136, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdptjiqwl6jyyr7, base_name=eevee, name=eevee, level=5 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: eevee, dex: 133, evolution_level: none}, {name: vaporeon, dex: 134, evolution_level: none}, {name: jolteon, dex: 135, evolution_level: none}, {name: flareon, dex: 136, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=eevee, dex=133, evolution_level=none logger.js:112:17
[unknown:0] Grid cell (2554,18683) maps to chain index 28 of 78 (rarity: common) logger.js:112:17
[unknown:0] Accepted landuse spawn in grid 28 (1/5) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 18 (team average 5 with deviation 13) logger.js:112:17
[unknown:0] Creating new Pokemon paras with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 18 (common): 4665 logger.js:112:17
[unknown:0] Initialized paras (Lvl 18) with 4665 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: psyduck logger.js:112:17
[unknown:0] Pokemon family for psyduck (chain 23): psyduck (Dex #54, Evo level: 33), golduck (Dex #55, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdptjiqyveqzwen, base_name=psyduck, name=psyduck, level=4 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: psyduck, dex: 54, evolution_level: 33}, {name: golduck, dex: 55, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=psyduck, dex=54, evolution_level=33 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] Starting landuse lookup for psyduck at 50.49589595269381,10.858045760505275 logger.js:112:17
[unknown:0] [LANDUSE_LOOKUP] Starting landuse lookup for point 50.49589595269381, 10.858045760505275 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] Checking 23 landuse features for point 50.49589595269381, 10.858045760505275 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] ❌ No valid landuse match found for point 50.49589595269381, 10.858045760505275 logger.js:138:17
[unknown:0] [LANDUSE_DEBUG] Landuse lookup completed for psyduck: logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache available: YES logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache features: 23 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Landuse result: null logger.js:112:17
[unknown:0] [LANDUSE_MISS] ⚪ Standard spawn without landuse (normal for random spawns) logger.js:112:17
[unknown:0] Grid cell (2555,18683) maps to chain index 22 of 78 (rarity: common) logger.js:112:17
[unknown:0] [GRID] Accepted spawn in grid 22 (1/5) logger.js:112:17
[unknown:0] Grid cell (2555,18681) maps to chain index 3 of 78 (rarity: common) logger.js:112:17
[unknown:0] Found base Pokemon for chain 4: caterpie (10) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 6 (team average 5 with deviation 1) logger.js:112:17
[unknown:0] Creating new Pokemon caterpie with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 6 (common): 172 logger.js:112:17
[unknown:0] Initialized caterpie (Lvl 6) with 172 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: paras logger.js:112:17
[unknown:0] Pokemon family for paras (chain 19): paras (Dex #46, Evo level: 24), parasect (Dex #47, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdptjiqy4hzb5t5, base_name=paras, name=paras, level=18 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: paras, dex: 46, evolution_level: 24}, {name: parasect, dex: 47, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=paras, dex=46, evolution_level=24 logger.js:112:17
[unknown:0] Grid cell (2556,18683) maps to chain index 35 of 78 (rarity: common) logger.js:112:17
[unknown:0] Accepted landuse spawn in grid 35 (1/5) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 1 (team average 5 with deviation -19) logger.js:112:17
[unknown:0] Creating new Pokemon vileplume with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Found Pokemon by exact name match: caterpie logger.js:112:17
[unknown:0] Pokemon family for caterpie (chain 4): caterpie (Dex #10, Evo level: 7), metapod (Dex #11, Evo level: 10), butterfree (Dex #12, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdptjiqy829pcq8, base_name=caterpie, name=caterpie, level=6 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: caterpie, dex: 10, evolution_level: 7}, {name: metapod, dex: 11, evolution_level: 10}, {name: butterfree, dex: 12, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=caterpie, dex=10, evolution_level=7 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] Starting landuse lookup for caterpie at 50.490713078705134,10.85675040855644 logger.js:112:17
[unknown:0] [LANDUSE_LOOKUP] Starting landuse lookup for point 50.490713078705134, 10.85675040855644 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] Checking 23 landuse features for point 50.490713078705134, 10.85675040855644 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] ❌ No valid landuse match found for point 50.490713078705134, 10.85675040855644 logger.js:138:17
[unknown:0] [LANDUSE_DEBUG] Landuse lookup completed for caterpie: logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache available: YES logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache features: 23 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Landuse result: null logger.js:112:17
[unknown:0] [LANDUSE_MISS] ⚪ Standard spawn without landuse (normal for random spawns) logger.js:112:17
[unknown:0] Grid cell (2555,18681) maps to chain index 3 of 78 (rarity: common) logger.js:112:17
[unknown:0] [GRID] Accepted spawn in grid 3 (2/5) logger.js:112:17
[unknown:0] Grid cell (2555,18681) maps to chain index 3 of 78 (rarity: common) logger.js:112:17
[unknown:0] Found base Pokemon for chain 4: caterpie (10) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 5 (exact match to team average) logger.js:112:17
[unknown:0] Creating new Pokemon caterpie with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized caterpie (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: vileplume logger.js:112:17
[unknown:0] Pokemon family for vileplume (chain 18): oddish (Dex #43, Evo level: 21), gloom (Dex #44, Evo level: 42), vileplume (Dex #45, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdptjir0xzthj5k, base_name=vileplume, name=vileplume, level=1 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: oddish, dex: 43, evolution_level: 21}, {name: gloom, dex: 44, evolution_level: 42}, {name: vileplume, dex: 45, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=oddish, dex=43, evolution_level=21 logger.js:112:17
[unknown:0] Grid cell (2556,18683) maps to chain index 35 of 78 (rarity: common) logger.js:112:17
[unknown:0] Accepted landuse spawn in grid 35 (2/5) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 8 (team average 5 with deviation 3) logger.js:112:17
[unknown:0] Creating new Pokemon onix with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 8 (common): 409 logger.js:112:17
[unknown:0] Initialized onix (Lvl 8) with 409 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: caterpie logger.js:112:17
[unknown:0] Pokemon family for caterpie (chain 4): caterpie (Dex #10, Evo level: 7), metapod (Dex #11, Evo level: 10), butterfree (Dex #12, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdptjir06nzxb7e, base_name=caterpie, name=caterpie, level=5 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: caterpie, dex: 10, evolution_level: 7}, {name: metapod, dex: 11, evolution_level: 10}, {name: butterfree, dex: 12, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=caterpie, dex=10, evolution_level=7 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] Starting landuse lookup for caterpie at 50.489442660719305,10.857700343488084 logger.js:112:17
[unknown:0] [LANDUSE_LOOKUP] Starting landuse lookup for point 50.489442660719305, 10.857700343488084 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] Checking 23 landuse features for point 50.489442660719305, 10.857700343488084 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] ❌ No valid landuse match found for point 50.489442660719305, 10.857700343488084 logger.js:138:17
[unknown:0] [LANDUSE_DEBUG] Landuse lookup completed for caterpie: logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache available: YES logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache features: 23 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Landuse result: null logger.js:112:17
[unknown:0] [LANDUSE_MISS] ⚪ Standard spawn without landuse (normal for random spawns) logger.js:112:17
[unknown:0] Grid cell (2555,18681) maps to chain index 3 of 78 (rarity: common) logger.js:112:17
[unknown:0] [GRID] Accepted spawn in grid 3 (3/5) logger.js:112:17
[unknown:0] Grid cell (2555,18681) maps to chain index 3 of 78 (rarity: common) logger.js:112:17
[unknown:0] Found base Pokemon for chain 4: caterpie (10) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 1 (team average 5 with deviation -7) logger.js:112:17
[unknown:0] Creating new Pokemon caterpie with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Found Pokemon by exact name match: onix logger.js:112:17
[unknown:0] Pokemon family for onix (chain 41): onix (Dex #95, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdptjir1a7qm7zm, base_name=onix, name=onix, level=8 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: onix, dex: 95, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=onix, dex=95, evolution_level=none logger.js:112:17
[unknown:0] Grid cell (2555,18684) maps to chain index 52 of 78 (rarity: scarce) logger.js:112:17
[unknown:0] Accepted landuse spawn in grid 52 (1/5) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 5 (exact match to team average) logger.js:112:17
[unknown:0] Creating new Pokemon graveler with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized graveler (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: caterpie logger.js:112:17
[unknown:0] Pokemon family for caterpie (chain 4): caterpie (Dex #10, Evo level: 7), metapod (Dex #11, Evo level: 10), butterfree (Dex #12, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdptjir2iyhp6pm, base_name=caterpie, name=caterpie, level=1 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: caterpie, dex: 10, evolution_level: 7}, {name: metapod, dex: 11, evolution_level: 10}, {name: butterfree, dex: 12, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=caterpie, dex=10, evolution_level=7 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] Starting landuse lookup for caterpie at 50.490377843247096,10.858184145851398 logger.js:112:17
[unknown:0] [LANDUSE_LOOKUP] Starting landuse lookup for point 50.490377843247096, 10.858184145851398 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] Checking 23 landuse features for point 50.490377843247096, 10.858184145851398 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] ❌ No valid landuse match found for point 50.490377843247096, 10.858184145851398 logger.js:138:17
[unknown:0] [LANDUSE_DEBUG] Landuse lookup completed for caterpie: logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache available: YES logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache features: 23 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Landuse result: null logger.js:112:17
[unknown:0] [LANDUSE_MISS] ⚪ Standard spawn without landuse (normal for random spawns) logger.js:112:17
[unknown:0] Grid cell (2555,18681) maps to chain index 3 of 78 (rarity: common) logger.js:112:17
[unknown:0] [GRID] Accepted spawn in grid 3 (4/5) logger.js:112:17
[unknown:0] Grid cell (2555,18682) maps to chain index 30 of 78 (rarity: common) logger.js:112:17
[unknown:0] Found base Pokemon for chain 31: geodude (74) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 3 (team average 5 with deviation -2) logger.js:112:17
[unknown:0] Creating new Pokemon geodude with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 3 (common): 21 logger.js:112:17
[unknown:0] Initialized geodude (Lvl 3) with 21 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: graveler logger.js:112:17
[unknown:0] Pokemon family for graveler (chain 31): geodude (Dex #74, Evo level: 25), graveler (Dex #75, Evo level: 50), golem (Dex #76, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdptjir2xzohlxk, base_name=graveler, name=graveler, level=5 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: geodude, dex: 74, evolution_level: 25}, {name: graveler, dex: 75, evolution_level: 50}, {name: golem, dex: 76, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=geodude, dex=74, evolution_level=25 logger.js:112:17
[unknown:0] Grid cell (2555,18684) maps to chain index 52 of 78 (rarity: scarce) logger.js:112:17
[unknown:0] Accepted landuse spawn in grid 52 (2/5) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 1 (team average 5 with deviation -5) logger.js:112:17
[unknown:0] Creating new Pokemon eevee with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Found Pokemon by exact name match: geodude logger.js:112:17
[unknown:0] Pokemon family for geodude (chain 31): geodude (Dex #74, Evo level: 25), graveler (Dex #75, Evo level: 50), golem (Dex #76, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdptjir3uhoi71v, base_name=geodude, name=geodude, level=3 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: geodude, dex: 74, evolution_level: 25}, {name: graveler, dex: 75, evolution_level: 50}, {name: golem, dex: 76, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=geodude, dex=74, evolution_level=25 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] Starting landuse lookup for geodude at 50.492013984138026,10.8563836091585 logger.js:112:17
[unknown:0] [LANDUSE_LOOKUP] Starting landuse lookup for point 50.492013984138026, 10.8563836091585 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] Checking 23 landuse features for point 50.492013984138026, 10.8563836091585 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] ❌ No valid landuse match found for point 50.492013984138026, 10.8563836091585 logger.js:138:17
[unknown:0] [LANDUSE_DEBUG] Landuse lookup completed for geodude: logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache available: YES logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache features: 23 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Landuse result: null logger.js:112:17
[unknown:0] [LANDUSE_MISS] ⚪ Standard spawn without landuse (normal for random spawns) logger.js:112:17
[unknown:0] Grid cell (2555,18682) maps to chain index 30 of 78 (rarity: common) logger.js:112:17
[unknown:0] [GRID] Accepted spawn in grid 30 (3/5) logger.js:112:17
[unknown:0] Grid cell (2555,18682) maps to chain index 30 of 78 (rarity: common) logger.js:112:17
[unknown:0] Found base Pokemon for chain 31: geodude (74) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 4 (team average 5 with deviation -1) logger.js:112:17
[unknown:0] Creating new Pokemon geodude with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 4 (common): 51 logger.js:112:17
[unknown:0] Initialized geodude (Lvl 4) with 51 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: eevee logger.js:112:17
[unknown:0] Pokemon family for eevee (chain 67): eevee (Dex #133, Evo level: null), vaporeon (Dex #134, Evo level: null), jolteon (Dex #135, Evo level: null), flareon (Dex #136, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdptjir4zntek0g, base_name=eevee, name=eevee, level=1 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: eevee, dex: 133, evolution_level: none}, {name: vaporeon, dex: 134, evolution_level: none}, {name: jolteon, dex: 135, evolution_level: none}, {name: flareon, dex: 136, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=eevee, dex=133, evolution_level=none logger.js:112:17
[unknown:0] Grid cell (2554,18683) maps to chain index 28 of 78 (rarity: common) logger.js:112:17
[unknown:0] Accepted landuse spawn in grid 28 (2/5) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 5 (exact match to team average) logger.js:112:17
[unknown:0] Creating new Pokemon vileplume with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized vileplume (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: geodude logger.js:112:17
[unknown:0] Pokemon family for geodude (chain 31): geodude (Dex #74, Evo level: 25), graveler (Dex #75, Evo level: 50), golem (Dex #76, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdptjir5lau87vc, base_name=geodude, name=geodude, level=4 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: geodude, dex: 74, evolution_level: 25}, {name: graveler, dex: 75, evolution_level: 50}, {name: golem, dex: 76, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=geodude, dex=74, evolution_level=25 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] Starting landuse lookup for geodude at 50.49309036393784,10.855936579558145 logger.js:112:17
[unknown:0] [LANDUSE_LOOKUP] Starting landuse lookup for point 50.49309036393784, 10.855936579558145 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] Checking 23 landuse features for point 50.49309036393784, 10.855936579558145 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] ❌ No valid landuse match found for point 50.49309036393784, 10.855936579558145 logger.js:138:17
[unknown:0] [LANDUSE_DEBUG] Landuse lookup completed for geodude: logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache available: YES logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache features: 23 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Landuse result: null logger.js:112:17
[unknown:0] [LANDUSE_MISS] ⚪ Standard spawn without landuse (normal for random spawns) logger.js:112:17
[unknown:0] Grid cell (2555,18682) maps to chain index 30 of 78 (rarity: common) logger.js:112:17
[unknown:0] [GRID] Accepted spawn in grid 30 (4/5) logger.js:112:17
[unknown:0] Grid cell (2555,18682) maps to chain index 30 of 78 (rarity: common) logger.js:112:17
[unknown:0] Found base Pokemon for chain 31: geodude (74) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 5 (exact match to team average) logger.js:112:17
[unknown:0] Creating new Pokemon geodude with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized geodude (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: vileplume logger.js:112:17
[unknown:0] Pokemon family for vileplume (chain 18): oddish (Dex #43, Evo level: 21), gloom (Dex #44, Evo level: 42), vileplume (Dex #45, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdptjir5nf3huo2, base_name=vileplume, name=vileplume, level=5 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: oddish, dex: 43, evolution_level: 21}, {name: gloom, dex: 44, evolution_level: 42}, {name: vileplume, dex: 45, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=oddish, dex=43, evolution_level=21 logger.js:112:17
[unknown:0] Grid cell (2557,18683) maps to chain index 10 of 78 (rarity: common) logger.js:112:17
[unknown:0] Accepted landuse spawn in grid 10 (1/5) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 5 (exact match to team average) logger.js:112:17
[unknown:0] Creating new Pokemon golem with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized golem (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: geodude logger.js:112:17
[unknown:0] Pokemon family for geodude (chain 31): geodude (Dex #74, Evo level: 25), graveler (Dex #75, Evo level: 50), golem (Dex #76, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdptjir6uj6jod8, base_name=geodude, name=geodude, level=5 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: geodude, dex: 74, evolution_level: 25}, {name: graveler, dex: 75, evolution_level: 50}, {name: golem, dex: 76, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=geodude, dex=74, evolution_level=25 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] Starting landuse lookup for geodude at 50.492543941394196,10.85610617345199 logger.js:112:17
[unknown:0] [LANDUSE_LOOKUP] Starting landuse lookup for point 50.492543941394196, 10.85610617345199 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] Checking 23 landuse features for point 50.492543941394196, 10.85610617345199 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] ❌ No valid landuse match found for point 50.492543941394196, 10.85610617345199 logger.js:138:17
[unknown:0] [LANDUSE_DEBUG] Landuse lookup completed for geodude: logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache available: YES logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache features: 23 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Landuse result: null logger.js:112:17
[unknown:0] [LANDUSE_MISS] ⚪ Standard spawn without landuse (normal for random spawns) logger.js:112:17
[unknown:0] Grid cell (2555,18682) maps to chain index 30 of 78 (rarity: common) logger.js:112:17
[unknown:0] [GRID] Accepted spawn in grid 30 (5/5) logger.js:112:17
[unknown:0] Grid cell (2555,18681) maps to chain index 3 of 78 (rarity: common) logger.js:112:17
[unknown:0] Found base Pokemon for chain 4: caterpie (10) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 5 (exact match to team average) logger.js:112:17
[unknown:0] Creating new Pokemon caterpie with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized caterpie (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: golem logger.js:112:17
[unknown:0] Pokemon family for golem (chain 31): geodude (Dex #74, Evo level: 25), graveler (Dex #75, Evo level: 50), golem (Dex #76, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdptjir6g81s15o, base_name=golem, name=golem, level=5 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: geodude, dex: 74, evolution_level: 25}, {name: graveler, dex: 75, evolution_level: 50}, {name: golem, dex: 76, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=geodude, dex=74, evolution_level=25 logger.js:112:17
[unknown:0] Grid cell (2555,18684) maps to chain index 52 of 78 (rarity: scarce) logger.js:112:17
[unknown:0] Accepted landuse spawn in grid 52 (3/5) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 8 (team average 5 with deviation 3) logger.js:112:17
[unknown:0] Creating new Pokemon pidgeot with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 8 (common): 409 logger.js:112:17
[unknown:0] Initialized pidgeot (Lvl 8) with 409 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: caterpie logger.js:112:17
[unknown:0] Pokemon family for caterpie (chain 4): caterpie (Dex #10, Evo level: 7), metapod (Dex #11, Evo level: 10), butterfree (Dex #12, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdptjir6je7yjdm, base_name=caterpie, name=caterpie, level=5 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: caterpie, dex: 10, evolution_level: 7}, {name: metapod, dex: 11, evolution_level: 10}, {name: butterfree, dex: 12, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=caterpie, dex=10, evolution_level=7 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] Starting landuse lookup for caterpie at 50.49022855280892,10.857061026474456 logger.js:112:17
[unknown:0] [LANDUSE_LOOKUP] Starting landuse lookup for point 50.49022855280892, 10.857061026474456 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] Checking 23 landuse features for point 50.49022855280892, 10.857061026474456 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] ❌ No valid landuse match found for point 50.49022855280892, 10.857061026474456 logger.js:138:17
[unknown:0] [LANDUSE_DEBUG] Landuse lookup completed for caterpie: logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache available: YES logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache features: 23 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Landuse result: null logger.js:112:17
[unknown:0] [LANDUSE_MISS] ⚪ Standard spawn without landuse (normal for random spawns) logger.js:112:17
[unknown:0] Grid cell (2555,18681) maps to chain index 3 of 78 (rarity: common) logger.js:112:17
[unknown:0] [GRID] Accepted spawn in grid 3 (5/5) logger.js:112:17
[unknown:0] Grid cell (2556,18681) maps to chain index 45 of 78 (rarity: common) logger.js:112:17
[unknown:0] Found base Pokemon for chain 46: cubone (104) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 6 (team average 5 with deviation 1) logger.js:112:17
[unknown:0] Creating new Pokemon cubone with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 6 (common): 172 logger.js:112:17
[unknown:0] Initialized cubone (Lvl 6) with 172 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: pidgeot logger.js:112:17
[unknown:0] Pokemon family for pidgeot (chain 6): pidgey (Dex #16, Evo level: 18), pidgeotto (Dex #17, Evo level: 36), pidgeot (Dex #18, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdptjir7miqwvqc, base_name=pidgeot, name=pidgeot, level=8 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: pidgey, dex: 16, evolution_level: 18}, {name: pidgeotto, dex: 17, evolution_level: 36}, {name: pidgeot, dex: 18, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=pidgey, dex=16, evolution_level=18 logger.js:112:17
[unknown:0] Grid cell (2557,18681) maps to chain index 65 of 78 (rarity: common) logger.js:112:17
[unknown:0] Accepted landuse spawn in grid 65 (1/5) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 4 (team average 5 with deviation -1) logger.js:112:17
[unknown:0] Creating new Pokemon porygon with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 4 (common): 51 logger.js:112:17
[unknown:0] Initialized porygon (Lvl 4) with 51 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: cubone logger.js:112:17
[unknown:0] Pokemon family for cubone (chain 46): cubone (Dex #104, Evo level: 28), marowak (Dex #105, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdptjir89xvhwll, base_name=cubone, name=cubone, level=6 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: cubone, dex: 104, evolution_level: 28}, {name: marowak, dex: 105, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=cubone, dex=104, evolution_level=28 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] Starting landuse lookup for cubone at 50.48935719222174,10.859786194075973 logger.js:112:17
[unknown:0] [LANDUSE_LOOKUP] Starting landuse lookup for point 50.48935719222174, 10.859786194075973 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] Checking 23 landuse features for point 50.48935719222174, 10.859786194075973 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] ❌ No valid landuse match found for point 50.48935719222174, 10.859786194075973 logger.js:138:17
[unknown:0] [LANDUSE_DEBUG] Landuse lookup completed for cubone: logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache available: YES logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache features: 23 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Landuse result: null logger.js:112:17
[unknown:0] [LANDUSE_MISS] ⚪ Standard spawn without landuse (normal for random spawns) logger.js:112:17
[unknown:0] Grid cell (2556,18681) maps to chain index 45 of 78 (rarity: common) logger.js:112:17
[unknown:0] [GRID] Accepted spawn in grid 45 (1/5) logger.js:112:17
[unknown:0] Grid cell (2555,18682) maps to chain index 30 of 78 (rarity: common) logger.js:112:17
[unknown:0] Found base Pokemon for chain 31: geodude (74) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 1 (team average 5 with deviation -10) logger.js:112:17
[unknown:0] Creating new Pokemon geodude with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Found Pokemon by exact name match: porygon logger.js:112:17
[unknown:0] Pokemon family for porygon (chain 68): porygon (Dex #137, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdptjir81znq4to, base_name=porygon, name=porygon, level=4 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: porygon, dex: 137, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=porygon, dex=137, evolution_level=none logger.js:112:17
[unknown:0] Grid cell (2552,18680) maps to chain index 36 of 78 (rarity: common) logger.js:112:17
[unknown:0] Accepted landuse spawn in grid 36 (1/5) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 5 (exact match to team average) logger.js:112:17
[unknown:0] Creating new Pokemon exeggcute with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized exeggcute (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: geodude logger.js:112:17
[unknown:0] Pokemon family for geodude (chain 31): geodude (Dex #74, Evo level: 25), graveler (Dex #75, Evo level: 50), golem (Dex #76, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdptjir9f5g364l, base_name=geodude, name=geodude, level=1 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: geodude, dex: 74, evolution_level: 25}, {name: graveler, dex: 75, evolution_level: 50}, {name: golem, dex: 76, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=geodude, dex=74, evolution_level=25 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] Starting landuse lookup for geodude at 50.49256804410895,10.85731602950415 logger.js:112:17
[unknown:0] [LANDUSE_LOOKUP] Starting landuse lookup for point 50.49256804410895, 10.85731602950415 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] Checking 23 landuse features for point 50.49256804410895, 10.85731602950415 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] ❌ No valid landuse match found for point 50.49256804410895, 10.85731602950415 logger.js:138:17
[unknown:0] [LANDUSE_DEBUG] Landuse lookup completed for geodude: logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache available: YES logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache features: 23 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Landuse result: null logger.js:112:17
[unknown:0] [LANDUSE_MISS] ⚪ Standard spawn without landuse (normal for random spawns) logger.js:112:17
[unknown:0] Grid cell (2555,18682) maps to chain index 30 of 78 (rarity: common) logger.js:112:17
[unknown:0] [GRID] Rejected spawn in grid 30 - cell full, retrying... logger.js:112:17
[unknown:0] Grid cell (2555,18682) maps to chain index 30 of 78 (rarity: common) logger.js:112:17
[unknown:0] Found base Pokemon for chain 31: geodude (74) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 6 (team average 5 with deviation 1) logger.js:112:17
[unknown:0] Creating new Pokemon geodude with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 6 (common): 172 logger.js:112:17
[unknown:0] Initialized geodude (Lvl 6) with 172 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: exeggcute logger.js:112:17
[unknown:0] Pokemon family for exeggcute (chain 45): exeggcute (Dex #102, Evo level: 32), exeggutor (Dex #103, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdptjira6nupj2f, base_name=exeggcute, name=exeggcute, level=5 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: exeggcute, dex: 102, evolution_level: 32}, {name: exeggutor, dex: 103, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=exeggcute, dex=102, evolution_level=32 logger.js:112:17
[unknown:0] Grid cell (2558,18682) maps to chain index 56 of 78 (rarity: rare) logger.js:112:17
[unknown:0] Accepted landuse spawn in grid 56 (3/5) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 5 (exact match to team average) logger.js:112:17
[unknown:0] Creating new Pokemon rhydon with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized rhydon (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: geodude logger.js:112:17
[unknown:0] Pokemon family for geodude (chain 31): geodude (Dex #74, Evo level: 25), graveler (Dex #75, Evo level: 50), golem (Dex #76, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdptjirbsc0l5ye, base_name=geodude, name=geodude, level=6 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: geodude, dex: 74, evolution_level: 25}, {name: graveler, dex: 75, evolution_level: 50}, {name: golem, dex: 76, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=geodude, dex=74, evolution_level=25 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] Starting landuse lookup for geodude at 50.4922119943237,10.856034506776732 logger.js:112:17
[unknown:0] [LANDUSE_LOOKUP] Starting landuse lookup for point 50.4922119943237, 10.856034506776732 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] Checking 23 landuse features for point 50.4922119943237, 10.856034506776732 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] ❌ No valid landuse match found for point 50.4922119943237, 10.856034506776732 logger.js:138:17
[unknown:0] [LANDUSE_DEBUG] Landuse lookup completed for geodude: logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache available: YES logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache features: 23 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Landuse result: null logger.js:112:17
[unknown:0] [LANDUSE_MISS] ⚪ Standard spawn without landuse (normal for random spawns) logger.js:112:17
[unknown:0] Grid cell (2555,18682) maps to chain index 30 of 78 (rarity: common) logger.js:112:17
[unknown:0] [GRID] Rejected spawn in grid 30 - cell full, retrying... logger.js:112:17
[unknown:0] Grid cell (2555,18682) maps to chain index 30 of 78 (rarity: common) logger.js:112:17
[unknown:0] Found base Pokemon for chain 31: geodude (74) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 6 (team average 5 with deviation 1) logger.js:112:17
[unknown:0] Creating new Pokemon geodude with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 6 (common): 172 logger.js:112:17
[unknown:0] Initialized geodude (Lvl 6) with 172 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: rhydon logger.js:112:17
[unknown:0] Pokemon family for rhydon (chain 50): rhyhorn (Dex #111, Evo level: 42), rhydon (Dex #112, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdptjircwlo48rd, base_name=rhydon, name=rhydon, level=5 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: rhyhorn, dex: 111, evolution_level: 42}, {name: rhydon, dex: 112, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=rhyhorn, dex=111, evolution_level=42 logger.js:112:17
[unknown:0] Grid cell (2555,18684) maps to chain index 52 of 78 (rarity: scarce) logger.js:112:17
[unknown:0] Accepted landuse spawn in grid 52 (4/5) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 10 (team average 5 with deviation 5) logger.js:112:17
[unknown:0] Creating new Pokemon vileplume with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 10 (common): 800 logger.js:112:17
[unknown:0] Initialized vileplume (Lvl 10) with 800 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: geodude logger.js:112:17
[unknown:0] Pokemon family for geodude (chain 31): geodude (Dex #74, Evo level: 25), graveler (Dex #75, Evo level: 50), golem (Dex #76, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdptjircu4dpwpn, base_name=geodude, name=geodude, level=6 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: geodude, dex: 74, evolution_level: 25}, {name: graveler, dex: 75, evolution_level: 50}, {name: golem, dex: 76, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=geodude, dex=74, evolution_level=25 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] Starting landuse lookup for geodude at 50.49286967323676,10.855141838198376 logger.js:112:17
[unknown:0] [LANDUSE_LOOKUP] Starting landuse lookup for point 50.49286967323676, 10.855141838198376 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] Checking 23 landuse features for point 50.49286967323676, 10.855141838198376 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] ❌ No valid landuse match found for point 50.49286967323676, 10.855141838198376 logger.js:138:17
[unknown:0] [LANDUSE_DEBUG] Landuse lookup completed for geodude: logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache available: YES logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache features: 23 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Landuse result: null logger.js:112:17
[unknown:0] [LANDUSE_MISS] ⚪ Standard spawn without landuse (normal for random spawns) logger.js:112:17
[unknown:0] Grid cell (2555,18682) maps to chain index 30 of 78 (rarity: common) logger.js:112:17
[unknown:0] [GRID] Rejected spawn in grid 30 - cell full, retrying... logger.js:112:17
[unknown:0] Grid cell (2555,18682) maps to chain index 30 of 78 (rarity: common) logger.js:112:17
[unknown:0] Found base Pokemon for chain 31: geodude (74) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 1 (team average 5 with deviation -16) logger.js:112:17
[unknown:0] Creating new Pokemon geodude with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Found Pokemon by exact name match: vileplume logger.js:112:17
[unknown:0] Pokemon family for vileplume (chain 18): oddish (Dex #43, Evo level: 21), gloom (Dex #44, Evo level: 42), vileplume (Dex #45, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdptjirdfxxu77x, base_name=vileplume, name=vileplume, level=10 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: oddish, dex: 43, evolution_level: 21}, {name: gloom, dex: 44, evolution_level: 42}, {name: vileplume, dex: 45, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=oddish, dex=43, evolution_level=21 logger.js:112:17
[unknown:0] Grid cell (2556,18683) maps to chain index 35 of 78 (rarity: common) logger.js:112:17
[unknown:0] Accepted landuse spawn in grid 35 (3/5) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 1 (team average 5 with deviation -9) logger.js:112:17
[unknown:0] Creating new Pokemon onix with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Found Pokemon by exact name match: geodude logger.js:112:17
[unknown:0] Pokemon family for geodude (chain 31): geodude (Dex #74, Evo level: 25), graveler (Dex #75, Evo level: 50), golem (Dex #76, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdptjireyghbteo, base_name=geodude, name=geodude, level=1 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: geodude, dex: 74, evolution_level: 25}, {name: graveler, dex: 75, evolution_level: 50}, {name: golem, dex: 76, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=geodude, dex=74, evolution_level=25 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] Starting landuse lookup for geodude at 50.49220371041195,10.856280965628471 logger.js:112:17
[unknown:0] [LANDUSE_LOOKUP] Starting landuse lookup for point 50.49220371041195, 10.856280965628471 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] Checking 23 landuse features for point 50.49220371041195, 10.856280965628471 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] ❌ No valid landuse match found for point 50.49220371041195, 10.856280965628471 logger.js:138:17
[unknown:0] [LANDUSE_DEBUG] Landuse lookup completed for geodude: logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache available: YES logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache features: 23 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Landuse result: null logger.js:112:17
[unknown:0] [LANDUSE_MISS] ⚪ Standard spawn without landuse (normal for random spawns) logger.js:112:17
[unknown:0] Grid cell (2555,18682) maps to chain index 30 of 78 (rarity: common) logger.js:112:17
[unknown:0] [GRID] Rejected spawn in grid 30 - cell full, retrying... logger.js:112:17
[unknown:0] Grid cell (2554,18683) maps to chain index 28 of 78 (rarity: common) logger.js:112:17
[unknown:0] Found base Pokemon for chain 29: bellsprout (69) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 3 (team average 5 with deviation -2) logger.js:112:17
[unknown:0] Creating new Pokemon bellsprout with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 3 (common): 21 logger.js:112:17
[unknown:0] Initialized bellsprout (Lvl 3) with 21 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: onix logger.js:112:17
[unknown:0] Pokemon family for onix (chain 41): onix (Dex #95, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdptjirfohcv79c, base_name=onix, name=onix, level=1 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: onix, dex: 95, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=onix, dex=95, evolution_level=none logger.js:112:17
[unknown:0] Grid cell (2555,18684) maps to chain index 52 of 78 (rarity: scarce) logger.js:112:17
[unknown:0] Accepted landuse spawn in grid 52 (5/5) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 6 (team average 5 with deviation 1) logger.js:112:17
[unknown:0] Creating new Pokemon onix with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 6 (common): 172 logger.js:112:17
[unknown:0] Initialized onix (Lvl 6) with 172 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: bellsprout logger.js:112:17
[unknown:0] Pokemon family for bellsprout (chain 29): bellsprout (Dex #69, Evo level: 21), weepinbell (Dex #70, Evo level: 42), victreebel (Dex #71, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdptjirfon2dyfu, base_name=bellsprout, name=bellsprout, level=3 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: bellsprout, dex: 69, evolution_level: 21}, {name: weepinbell, dex: 70, evolution_level: 42}, {name: victreebel, dex: 71, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=bellsprout, dex=69, evolution_level=21 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] Starting landuse lookup for bellsprout at 50.49527061882798,10.853719161717105 logger.js:112:17
[unknown:0] [LANDUSE_LOOKUP] Starting landuse lookup for point 50.49527061882798, 10.853719161717105 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] Checking 23 landuse features for point 50.49527061882798, 10.853719161717105 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] ❌ No valid landuse match found for point 50.49527061882798, 10.853719161717105 logger.js:138:17
[unknown:0] [LANDUSE_DEBUG] Landuse lookup completed for bellsprout: logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache available: YES logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache features: 23 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Landuse result: null logger.js:112:17
[unknown:0] [LANDUSE_MISS] ⚪ Standard spawn without landuse (normal for random spawns) logger.js:112:17
[unknown:0] Grid cell (2554,18683) maps to chain index 28 of 78 (rarity: common) logger.js:112:17
[unknown:0] [GRID] Accepted spawn in grid 28 (2/5) logger.js:112:17
[unknown:0] Grid cell (2554,18681) maps to chain index 36 of 78 (rarity: common) logger.js:112:17
[unknown:0] Found base Pokemon for chain 37: seel (86) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 10 (team average 5 with deviation 5) logger.js:112:17
[unknown:0] Creating new Pokemon seel with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 10 (common): 800 logger.js:112:17
[unknown:0] Initialized seel (Lvl 10) with 800 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: onix logger.js:112:17
[unknown:0] Pokemon family for onix (chain 41): onix (Dex #95, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdptjirf4jeafq1, base_name=onix, name=onix, level=6 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: onix, dex: 95, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=onix, dex=95, evolution_level=none logger.js:112:17
[unknown:0] Grid cell (2555,18684) maps to chain index 52 of 78 (rarity: scarce) logger.js:112:17
[unknown:0] Rejected landuse spawn in grid 52 - cell full (5/5) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 6 (team average 5 with deviation 1) logger.js:112:17
[unknown:0] Creating new Pokemon gastly with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 6 (common): 172 logger.js:112:17
[unknown:0] Initialized gastly (Lvl 6) with 172 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: seel logger.js:112:17
[unknown:0] Pokemon family for seel (chain 37): seel (Dex #86, Evo level: 34), dewgong (Dex #87, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdptjirgltryqm8, base_name=seel, name=seel, level=10 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: seel, dex: 86, evolution_level: 34}, {name: dewgong, dex: 87, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=seel, dex=86, evolution_level=34 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] Starting landuse lookup for seel at 50.49054966628726,10.853516788920754 logger.js:112:17
[unknown:0] [LANDUSE_LOOKUP] Starting landuse lookup for point 50.49054966628726, 10.853516788920754 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] Checking 23 landuse features for point 50.49054966628726, 10.853516788920754 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] ❌ No valid landuse match found for point 50.49054966628726, 10.853516788920754 logger.js:138:17
[unknown:0] [LANDUSE_DEBUG] Landuse lookup completed for seel: logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache available: YES logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache features: 23 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Landuse result: null logger.js:112:17
[unknown:0] [LANDUSE_MISS] ⚪ Standard spawn without landuse (normal for random spawns) logger.js:112:17
[unknown:0] Grid cell (2554,18681) maps to chain index 36 of 78 (rarity: common) logger.js:112:17
[unknown:0] [GRID] Accepted spawn in grid 36 (2/5) logger.js:112:17
[unknown:0] Grid cell (2555,18681) maps to chain index 3 of 78 (rarity: common) logger.js:112:17
[unknown:0] Found base Pokemon for chain 4: caterpie (10) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 15 (team average 5 with deviation 10) logger.js:112:17
[unknown:0] Creating new Pokemon caterpie with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 15 (common): 2700 logger.js:112:17
[unknown:0] Initialized caterpie (Lvl 15) with 2700 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: gastly logger.js:112:17
[unknown:0] Pokemon family for gastly (chain 40): gastly (Dex #92, Evo level: 25), haunter (Dex #93, Evo level: 50), gengar (Dex #94, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdptjirhtcl2hqn, base_name=gastly, name=gastly, level=6 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: gastly, dex: 92, evolution_level: 25}, {name: haunter, dex: 93, evolution_level: 50}, {name: gengar, dex: 94, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=gastly, dex=92, evolution_level=25 logger.js:112:17
[unknown:0] Grid cell (2553,18681) maps to chain index 7 of 78 (rarity: common) logger.js:112:17
[unknown:0] Accepted landuse spawn in grid 7 (1/5) logger.js:112:17
[unknown:0] Landuse grid distribution summary: 10 cells used, max per cell: 5 logger.js:112:17
[unknown:0]   Landuse Grid 61: 2 Pokemon logger.js:112:17
[unknown:0]   Landuse Grid 1: 1 Pokemon logger.js:112:17
[unknown:0]   Landuse Grid 56: 3 Pokemon logger.js:112:17
[unknown:0]   Landuse Grid 28: 2 Pokemon logger.js:112:17
[unknown:0]   Landuse Grid 35: 3 Pokemon logger.js:112:17
[unknown:0]   Landuse Grid 52: 5 Pokemon logger.js:112:17
[unknown:0]   Landuse Grid 10: 1 Pokemon logger.js:112:17
[unknown:0]   Landuse Grid 65: 1 Pokemon logger.js:112:17
[unknown:0]   Landuse Grid 36: 1 Pokemon logger.js:112:17
[unknown:0]   Landuse Grid 7: 1 Pokemon logger.js:112:17
[unknown:0] Creating new Pokemon magneton with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 3 (common): 21 logger.js:112:17
[unknown:0] Initialized magneton (Lvl 3) with 21 XP logger.js:112:17
[unknown:0] Created final Pokemon: magnemite (base: magneton) Level 3, Dex #81 [Landuse Special] logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon magneton with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 2 (common): 6 logger.js:112:17
[unknown:0] Initialized magneton (Lvl 2) with 6 XP logger.js:112:17
[unknown:0] Created final Pokemon: magnemite (base: magneton) Level 2, Dex #81 [Landuse Special] logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon tauros with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Created final Pokemon: tauros (base: tauros) Level 1, Dex #128 [Landuse Special] logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon pidgeot with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Created final Pokemon: pidgey (base: pidgeot) Level 1, Dex #16 [Landuse Special] logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon snorlax with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized snorlax (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] Created final Pokemon: snorlax (base: snorlax) Level 5, Dex #143 [Landuse Special] logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon eevee with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized eevee (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] Created final Pokemon: eevee (base: eevee) Level 5, Dex #133 [Landuse Special] logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon paras with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 18 (common): 4665 logger.js:112:17
[unknown:0] Initialized paras (Lvl 18) with 4665 XP logger.js:112:17
[unknown:0] Created final Pokemon: paras (base: paras) Level 18, Dex #46 [Landuse Special] logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon vileplume with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Created final Pokemon: oddish (base: vileplume) Level 1, Dex #43 [Landuse Special] logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon onix with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 8 (common): 409 logger.js:112:17
[unknown:0] Initialized onix (Lvl 8) with 409 XP logger.js:112:17
[unknown:0] Created final Pokemon: onix (base: onix) Level 8, Dex #95 [Landuse Special] logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon graveler with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized graveler (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] Created final Pokemon: geodude (base: graveler) Level 5, Dex #74 [Landuse Special] logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon eevee with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Created final Pokemon: eevee (base: eevee) Level 1, Dex #133 [Landuse Special] logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon vileplume with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized vileplume (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] Created final Pokemon: oddish (base: vileplume) Level 5, Dex #43 [Landuse Special] logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon golem with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized golem (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] Created final Pokemon: geodude (base: golem) Level 5, Dex #74 [Landuse Special] logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon pidgeot with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 8 (common): 409 logger.js:112:17
[unknown:0] Initialized pidgeot (Lvl 8) with 409 XP logger.js:112:17
[unknown:0] Created final Pokemon: pidgey (base: pidgeot) Level 8, Dex #16 [Landuse Special] logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon porygon with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 4 (common): 51 logger.js:112:17
[unknown:0] Initialized porygon (Lvl 4) with 51 XP logger.js:112:17
[unknown:0] Created final Pokemon: porygon (base: porygon) Level 4, Dex #137 [Landuse Special] logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon exeggcute with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized exeggcute (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] Created final Pokemon: exeggcute (base: exeggcute) Level 5, Dex #102 [Landuse Special] logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon rhydon with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized rhydon (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] Created final Pokemon: rhyhorn (base: rhydon) Level 5, Dex #111 [Landuse Special] logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon vileplume with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 10 (common): 800 logger.js:112:17
[unknown:0] Initialized vileplume (Lvl 10) with 800 XP logger.js:112:17
[unknown:0] Created final Pokemon: oddish (base: vileplume) Level 10, Dex #43 [Landuse Special] logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon onix with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Created final Pokemon: onix (base: onix) Level 1, Dex #95 [Landuse Special] logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon gastly with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 6 (common): 172 logger.js:112:17
[unknown:0] Initialized gastly (Lvl 6) with 172 XP logger.js:112:17
[unknown:0] Created final Pokemon: gastly (base: gastly) Level 6, Dex #92 [Landuse Special] logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] [LANDUSE_SPAWN] ✅ Successfully created 20 landuse Pokemon with isolated spawning system logger.js:138:17
[unknown:0] Found Pokemon by exact name match: caterpie logger.js:112:17
[unknown:0] Pokemon family for caterpie (chain 4): caterpie (Dex #10, Evo level: 7), metapod (Dex #11, Evo level: 10), butterfree (Dex #12, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdptjirhws5ldkr, base_name=caterpie, name=caterpie, level=15 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: caterpie, dex: 10, evolution_level: 7}, {name: metapod, dex: 11, evolution_level: 10}, {name: butterfree, dex: 12, evolution_level: none} logger.js:112:17
[unknown:0] Evolution by level: caterpie -> metapod (Level: 15 >= 7) logger.js:112:17
[unknown:0] Evolution by level: metapod -> butterfree (Level: 15 >= 10) logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=butterfree, dex=12, evolution_level=none logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] Starting landuse lookup for caterpie at 50.49177242012675,10.857630627644967 logger.js:112:17
[unknown:0] [LANDUSE_LOOKUP] Starting landuse lookup for point 50.49177242012675, 10.857630627644967 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] Checking 23 landuse features for point 50.49177242012675, 10.857630627644967 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] ❌ No valid landuse match found for point 50.49177242012675, 10.857630627644967 logger.js:138:17
[unknown:0] [LANDUSE_DEBUG] Landuse lookup completed for caterpie: logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache available: YES logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache features: 23 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Landuse result: null logger.js:112:17
[unknown:0] [LANDUSE_MISS] ⚪ Standard spawn without landuse (normal for random spawns) logger.js:112:17
[unknown:0] Grid cell (2555,18681) maps to chain index 3 of 78 (rarity: common) logger.js:112:17
[unknown:0] [GRID] Rejected spawn in grid 3 - cell full, retrying... logger.js:112:17
[unknown:0] Grid cell (2555,18681) maps to chain index 3 of 78 (rarity: common) logger.js:112:17
[unknown:0] Found base Pokemon for chain 4: caterpie (10) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 5 (exact match to team average) logger.js:112:17
[unknown:0] Creating new Pokemon caterpie with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized caterpie (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: caterpie logger.js:112:17
[unknown:0] Pokemon family for caterpie (chain 4): caterpie (Dex #10, Evo level: 7), metapod (Dex #11, Evo level: 10), butterfree (Dex #12, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdptjirmm1619c6, base_name=caterpie, name=caterpie, level=5 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: caterpie, dex: 10, evolution_level: 7}, {name: metapod, dex: 11, evolution_level: 10}, {name: butterfree, dex: 12, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=caterpie, dex=10, evolution_level=7 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] Starting landuse lookup for caterpie at 50.49115797508266,10.85703270683956 logger.js:112:17
[unknown:0] [LANDUSE_LOOKUP] Starting landuse lookup for point 50.49115797508266, 10.85703270683956 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] Checking 23 landuse features for point 50.49115797508266, 10.85703270683956 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] ❌ No valid landuse match found for point 50.49115797508266, 10.85703270683956 logger.js:138:17
[unknown:0] [LANDUSE_DEBUG] Landuse lookup completed for caterpie: logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache available: YES logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache features: 23 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Landuse result: null logger.js:112:17
[unknown:0] [LANDUSE_MISS] ⚪ Standard spawn without landuse (normal for random spawns) logger.js:112:17
[unknown:0] Grid cell (2555,18681) maps to chain index 3 of 78 (rarity: common) logger.js:112:17
[unknown:0] [GRID] Rejected spawn in grid 3 - cell full, retrying... logger.js:112:17
[unknown:0] Grid cell (2554,18682) maps to chain index 28 of 78 (rarity: common) logger.js:112:17
[unknown:0] Found base Pokemon for chain 29: bellsprout (69) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 1 (team average 5 with deviation -11) logger.js:112:17
[unknown:0] Creating new Pokemon bellsprout with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Found Pokemon by exact name match: bellsprout logger.js:112:17
[unknown:0] Pokemon family for bellsprout (chain 29): bellsprout (Dex #69, Evo level: 21), weepinbell (Dex #70, Evo level: 42), victreebel (Dex #71, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdptjirmxqv48xk, base_name=bellsprout, name=bellsprout, level=1 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: bellsprout, dex: 69, evolution_level: 21}, {name: weepinbell, dex: 70, evolution_level: 42}, {name: victreebel, dex: 71, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=bellsprout, dex=69, evolution_level=21 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] Starting landuse lookup for bellsprout at 50.49332510872419,10.853589044373644 logger.js:112:17
[unknown:0] [LANDUSE_LOOKUP] Starting landuse lookup for point 50.49332510872419, 10.853589044373644 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] Checking 23 landuse features for point 50.49332510872419, 10.853589044373644 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] ❌ No valid landuse match found for point 50.49332510872419, 10.853589044373644 logger.js:138:17
[unknown:0] [LANDUSE_DEBUG] Landuse lookup completed for bellsprout: logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache available: YES logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache features: 23 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Landuse result: null logger.js:112:17
[unknown:0] [LANDUSE_MISS] ⚪ Standard spawn without landuse (normal for random spawns) logger.js:112:17
[unknown:0] Grid cell (2554,18682) maps to chain index 28 of 78 (rarity: common) logger.js:112:17
[unknown:0] [GRID] Accepted spawn in grid 28 (3/5) logger.js:112:17
[unknown:0] Grid cell (2556,18681) maps to chain index 45 of 78 (rarity: common) logger.js:112:17
[unknown:0] Found base Pokemon for chain 46: cubone (104) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 5 (exact match to team average) logger.js:112:17
[unknown:0] Creating new Pokemon cubone with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized cubone (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: cubone logger.js:112:17
[unknown:0] Pokemon family for cubone (chain 46): cubone (Dex #104, Evo level: 28), marowak (Dex #105, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdptjirnf90suv7, base_name=cubone, name=cubone, level=5 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: cubone, dex: 104, evolution_level: 28}, {name: marowak, dex: 105, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=cubone, dex=104, evolution_level=28 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] Starting landuse lookup for cubone at 50.49144556208901,10.860556101502462 logger.js:112:17
[unknown:0] [LANDUSE_LOOKUP] Starting landuse lookup for point 50.49144556208901, 10.860556101502462 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] Checking 23 landuse features for point 50.49144556208901, 10.860556101502462 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] ❌ No valid landuse match found for point 50.49144556208901, 10.860556101502462 logger.js:138:17
[unknown:0] [LANDUSE_DEBUG] Landuse lookup completed for cubone: logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache available: YES logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache features: 23 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Landuse result: null logger.js:112:17
[unknown:0] [LANDUSE_MISS] ⚪ Standard spawn without landuse (normal for random spawns) logger.js:112:17
[unknown:0] Grid cell (2556,18681) maps to chain index 45 of 78 (rarity: common) logger.js:112:17
[unknown:0] [GRID] Accepted spawn in grid 45 (2/5) logger.js:112:17
[unknown:0] Grid cell (2555,18682) maps to chain index 30 of 78 (rarity: common) logger.js:112:17
[unknown:0] Found base Pokemon for chain 31: geodude (74) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 1 (team average 5 with deviation -7) logger.js:112:17
[unknown:0] Creating new Pokemon geodude with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Found Pokemon by exact name match: geodude logger.js:112:17
[unknown:0] Pokemon family for geodude (chain 31): geodude (Dex #74, Evo level: 25), graveler (Dex #75, Evo level: 50), golem (Dex #76, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdptjirnpgbbhbr, base_name=geodude, name=geodude, level=1 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: geodude, dex: 74, evolution_level: 25}, {name: graveler, dex: 75, evolution_level: 50}, {name: golem, dex: 76, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=geodude, dex=74, evolution_level=25 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] Starting landuse lookup for geodude at 50.4924725977316,10.856131288685646 logger.js:112:17
[unknown:0] [LANDUSE_LOOKUP] Starting landuse lookup for point 50.4924725977316, 10.856131288685646 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] Checking 23 landuse features for point 50.4924725977316, 10.856131288685646 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] ❌ No valid landuse match found for point 50.4924725977316, 10.856131288685646 logger.js:138:17
[unknown:0] [LANDUSE_DEBUG] Landuse lookup completed for geodude: logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache available: YES logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache features: 23 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Landuse result: null logger.js:112:17
[unknown:0] [LANDUSE_MISS] ⚪ Standard spawn without landuse (normal for random spawns) logger.js:112:17
[unknown:0] Grid cell (2555,18682) maps to chain index 30 of 78 (rarity: common) logger.js:112:17
[unknown:0] [GRID] Rejected spawn in grid 30 - cell full, retrying... logger.js:112:17
[unknown:0] Grid cell (2553,18682) maps to chain index 12 of 78 (rarity: common) logger.js:112:17
[unknown:0] Found base Pokemon for chain 13: nidoran-m (32) logger.js:112:17
[unknown:0] getTeamPokemon returning 0 Pokemon: logger.js:112:17
[unknown:0] No Pokemon in team, using default level 5 for spawns logger.js:112:17
[unknown:0] Spawn level: 5 (exact match to team average) logger.js:112:17
[unknown:0] Creating new Pokemon nidoran-m with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized nidoran-m (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] Found Pokemon by exact name match: nidoran-m logger.js:112:17
[unknown:0] Pokemon family for nidoran-m (chain 13): nidoran-m (Dex #32, Evo level: 16), nidorino (Dex #33, Evo level: 32), nidoking (Dex #34, Evo level: null) logger.js:112:17
[unknown:0] [getDisplayForm] Pokemon: id=mdptjirpgifw3q3, base_name=nidoran-m, name=nidoran-m, level=5 logger.js:112:17
[unknown:0] [getDisplayForm] Family: {name: nidoran-m, dex: 32, evolution_level: 16}, {name: nidorino, dex: 33, evolution_level: 32}, {name: nidoking, dex: 34, evolution_level: none} logger.js:112:17
[unknown:0] [getDisplayForm] Current stage: name=nidoran-m, dex=32, evolution_level=16 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] Starting landuse lookup for nidoran-m at 50.493553486746166,10.84952281906951 logger.js:112:17
[unknown:0] [LANDUSE_LOOKUP] Starting landuse lookup for point 50.493553486746166, 10.84952281906951 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] Checking 23 landuse features for point 50.493553486746166, 10.84952281906951 logger.js:138:17
[unknown:0] [LANDUSE_LOOKUP] ❌ No valid landuse match found for point 50.493553486746166, 10.84952281906951 logger.js:138:17
[unknown:0] [LANDUSE_DEBUG] Landuse lookup completed for nidoran-m: logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache available: YES logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Cache features: 23 logger.js:112:17
[unknown:0] [LANDUSE_DEBUG] - Landuse result: null logger.js:112:17
[unknown:0] [LANDUSE_MISS] ⚪ Standard spawn without landuse (normal for random spawns) logger.js:112:17
[unknown:0] Grid cell (2553,18682) maps to chain index 12 of 78 (rarity: common) logger.js:112:17
[unknown:0] [GRID] Accepted spawn in grid 12 (1/5) logger.js:112:17
[unknown:0] Grid distribution summary: 8 cells used, max per cell: 5 logger.js:112:17
[unknown:0]   Grid 21: 1 Pokemon logger.js:112:17
[unknown:0]   Grid 36: 2 Pokemon logger.js:112:17
[unknown:0]   Grid 28: 3 Pokemon logger.js:112:17
[unknown:0]   Grid 30: 5 Pokemon logger.js:112:17
[unknown:0]   Grid 3: 5 Pokemon logger.js:112:17
[unknown:0]   Grid 22: 1 Pokemon logger.js:112:17
[unknown:0]   Grid 45: 2 Pokemon logger.js:112:17
[unknown:0]   Grid 12: 1 Pokemon logger.js:112:17
[unknown:0] Creating new Pokemon meowth with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 4 (common): 51 logger.js:112:17
[unknown:0] Initialized meowth (Lvl 4) with 51 XP logger.js:112:17
[unknown:0] [CACHE_DEBUG] Applying standard spawn data for meowth: logger.js:112:17
[unknown:0] [CACHE_DEBUG] - pokemonData.landuseData = {"value":"residential"} logger.js:112:17
[unknown:0] [CACHE_DEBUG] - spawn location = 50.49412288826295,10.860413051208836 logger.js:112:17
[unknown:0] [CACHE_DEBUG] ✓ Set landuse data for meowth: residential logger.js:112:17
[unknown:0] Created final Pokemon: meowth (base: meowth) Level 4, Dex #52 logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon seel with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 2 (common): 6 logger.js:112:17
[unknown:0] Initialized seel (Lvl 2) with 6 XP logger.js:112:17
[unknown:0] [CACHE_DEBUG] Applying standard spawn data for seel: logger.js:112:17
[unknown:0] [CACHE_DEBUG] - pokemonData.landuseData = null logger.js:112:17
[unknown:0] [CACHE_DEBUG] - spawn location = 50.49174875304111,10.854156284031964 logger.js:112:17
[unknown:0] [CACHE_DEBUG] ✗ No landuse data available for seel - this indicates cache lookup failed logger.js:112:17
[unknown:0] Created final Pokemon: seel (base: seel) Level 2, Dex #86 logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon bellsprout with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 7 (common): 274 logger.js:112:17
[unknown:0] Initialized bellsprout (Lvl 7) with 274 XP logger.js:112:17
[unknown:0] [CACHE_DEBUG] Applying standard spawn data for bellsprout: logger.js:112:17
[unknown:0] [CACHE_DEBUG] - pokemonData.landuseData = null logger.js:112:17
[unknown:0] [CACHE_DEBUG] - spawn location = 50.494157711384396,10.852399739033908 logger.js:112:17
[unknown:0] [CACHE_DEBUG] ✗ No landuse data available for bellsprout - this indicates cache lookup failed logger.js:112:17
[unknown:0] Created final Pokemon: bellsprout (base: bellsprout) Level 7, Dex #69 logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon geodude with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized geodude (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] [CACHE_DEBUG] Applying standard spawn data for geodude: logger.js:112:17
[unknown:0] [CACHE_DEBUG] - pokemonData.landuseData = null logger.js:112:17
[unknown:0] [CACHE_DEBUG] - spawn location = 50.49414283667046,10.855925476484767 logger.js:112:17
[unknown:0] [CACHE_DEBUG] ✗ No landuse data available for geodude - this indicates cache lookup failed logger.js:112:17
[unknown:0] Created final Pokemon: geodude (base: geodude) Level 5, Dex #74 logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon caterpie with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 8 (common): 409 logger.js:112:17
[unknown:0] Initialized caterpie (Lvl 8) with 409 XP logger.js:112:17
[unknown:0] [CACHE_DEBUG] Applying standard spawn data for metapod: logger.js:112:17
[unknown:0] [CACHE_DEBUG] - pokemonData.landuseData = null logger.js:112:17
[unknown:0] [CACHE_DEBUG] - spawn location = 50.49104178413201,10.855307665272447 logger.js:112:17
[unknown:0] [CACHE_DEBUG] ✗ No landuse data available for metapod - this indicates cache lookup failed logger.js:112:17
[unknown:0] Created final Pokemon: metapod (base: caterpie) Level 8, Dex #11 logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon geodude with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized geodude (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] [CACHE_DEBUG] Applying standard spawn data for geodude: logger.js:112:17
[unknown:0] [CACHE_DEBUG] - pokemonData.landuseData = null logger.js:112:17
[unknown:0] [CACHE_DEBUG] - spawn location = 50.49231054474602,10.8554706066709 logger.js:112:17
[unknown:0] [CACHE_DEBUG] ✗ No landuse data available for geodude - this indicates cache lookup failed logger.js:112:17
[unknown:0] Created final Pokemon: geodude (base: geodude) Level 5, Dex #74 logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon psyduck with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 4 (common): 51 logger.js:112:17
[unknown:0] Initialized psyduck (Lvl 4) with 51 XP logger.js:112:17
[unknown:0] [CACHE_DEBUG] Applying standard spawn data for psyduck: logger.js:112:17
[unknown:0] [CACHE_DEBUG] - pokemonData.landuseData = null logger.js:112:17
[unknown:0] [CACHE_DEBUG] - spawn location = 50.49589595269381,10.858045760505275 logger.js:112:17
[unknown:0] [CACHE_DEBUG] ✗ No landuse data available for psyduck - this indicates cache lookup failed logger.js:112:17
[unknown:0] Created final Pokemon: psyduck (base: psyduck) Level 4, Dex #54 logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon caterpie with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 6 (common): 172 logger.js:112:17
[unknown:0] Initialized caterpie (Lvl 6) with 172 XP logger.js:112:17
[unknown:0] [CACHE_DEBUG] Applying standard spawn data for caterpie: logger.js:112:17
[unknown:0] [CACHE_DEBUG] - pokemonData.landuseData = null logger.js:112:17
[unknown:0] [CACHE_DEBUG] - spawn location = 50.490713078705134,10.85675040855644 logger.js:112:17
[unknown:0] [CACHE_DEBUG] ✗ No landuse data available for caterpie - this indicates cache lookup failed logger.js:112:17
[unknown:0] Created final Pokemon: caterpie (base: caterpie) Level 6, Dex #10 logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon caterpie with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized caterpie (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] [CACHE_DEBUG] Applying standard spawn data for caterpie: logger.js:112:17
[unknown:0] [CACHE_DEBUG] - pokemonData.landuseData = null logger.js:112:17
[unknown:0] [CACHE_DEBUG] - spawn location = 50.489442660719305,10.857700343488084 logger.js:112:17
[unknown:0] [CACHE_DEBUG] ✗ No landuse data available for caterpie - this indicates cache lookup failed logger.js:112:17
[unknown:0] Created final Pokemon: caterpie (base: caterpie) Level 5, Dex #10 logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon caterpie with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] [CACHE_DEBUG] Applying standard spawn data for caterpie: logger.js:112:17
[unknown:0] [CACHE_DEBUG] - pokemonData.landuseData = null logger.js:112:17
[unknown:0] [CACHE_DEBUG] - spawn location = 50.490377843247096,10.858184145851398 logger.js:112:17
[unknown:0] [CACHE_DEBUG] ✗ No landuse data available for caterpie - this indicates cache lookup failed logger.js:112:17
[unknown:0] Created final Pokemon: caterpie (base: caterpie) Level 1, Dex #10 logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon geodude with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 3 (common): 21 logger.js:112:17
[unknown:0] Initialized geodude (Lvl 3) with 21 XP logger.js:112:17
[unknown:0] [CACHE_DEBUG] Applying standard spawn data for geodude: logger.js:112:17
[unknown:0] [CACHE_DEBUG] - pokemonData.landuseData = null logger.js:112:17
[unknown:0] [CACHE_DEBUG] - spawn location = 50.492013984138026,10.8563836091585 logger.js:112:17
[unknown:0] [CACHE_DEBUG] ✗ No landuse data available for geodude - this indicates cache lookup failed logger.js:112:17
[unknown:0] Created final Pokemon: geodude (base: geodude) Level 3, Dex #74 logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon geodude with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 4 (common): 51 logger.js:112:17
[unknown:0] Initialized geodude (Lvl 4) with 51 XP logger.js:112:17
[unknown:0] [CACHE_DEBUG] Applying standard spawn data for geodude: logger.js:112:17
[unknown:0] [CACHE_DEBUG] - pokemonData.landuseData = null logger.js:112:17
[unknown:0] [CACHE_DEBUG] - spawn location = 50.49309036393784,10.855936579558145 logger.js:112:17
[unknown:0] [CACHE_DEBUG] ✗ No landuse data available for geodude - this indicates cache lookup failed logger.js:112:17
[unknown:0] Created final Pokemon: geodude (base: geodude) Level 4, Dex #74 logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon geodude with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized geodude (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] [CACHE_DEBUG] Applying standard spawn data for geodude: logger.js:112:17
[unknown:0] [CACHE_DEBUG] - pokemonData.landuseData = null logger.js:112:17
[unknown:0] [CACHE_DEBUG] - spawn location = 50.492543941394196,10.85610617345199 logger.js:112:17
[unknown:0] [CACHE_DEBUG] ✗ No landuse data available for geodude - this indicates cache lookup failed logger.js:112:17
[unknown:0] Created final Pokemon: geodude (base: geodude) Level 5, Dex #74 logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon caterpie with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized caterpie (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] [CACHE_DEBUG] Applying standard spawn data for caterpie: logger.js:112:17
[unknown:0] [CACHE_DEBUG] - pokemonData.landuseData = null logger.js:112:17
[unknown:0] [CACHE_DEBUG] - spawn location = 50.49022855280892,10.857061026474456 logger.js:112:17
[unknown:0] [CACHE_DEBUG] ✗ No landuse data available for caterpie - this indicates cache lookup failed logger.js:112:17
[unknown:0] Created final Pokemon: caterpie (base: caterpie) Level 5, Dex #10 logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon cubone with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 6 (common): 172 logger.js:112:17
[unknown:0] Initialized cubone (Lvl 6) with 172 XP logger.js:112:17
[unknown:0] [CACHE_DEBUG] Applying standard spawn data for cubone: logger.js:112:17
[unknown:0] [CACHE_DEBUG] - pokemonData.landuseData = null logger.js:112:17
[unknown:0] [CACHE_DEBUG] - spawn location = 50.48935719222174,10.859786194075973 logger.js:112:17
[unknown:0] [CACHE_DEBUG] ✗ No landuse data available for cubone - this indicates cache lookup failed logger.js:112:17
[unknown:0] Created final Pokemon: cubone (base: cubone) Level 6, Dex #104 logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon bellsprout with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 3 (common): 21 logger.js:112:17
[unknown:0] Initialized bellsprout (Lvl 3) with 21 XP logger.js:112:17
[unknown:0] [CACHE_DEBUG] Applying standard spawn data for bellsprout: logger.js:112:17
[unknown:0] [CACHE_DEBUG] - pokemonData.landuseData = null logger.js:112:17
[unknown:0] [CACHE_DEBUG] - spawn location = 50.49527061882798,10.853719161717105 logger.js:112:17
[unknown:0] [CACHE_DEBUG] ✗ No landuse data available for bellsprout - this indicates cache lookup failed logger.js:112:17
[unknown:0] Created final Pokemon: bellsprout (base: bellsprout) Level 3, Dex #69 logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon seel with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 10 (common): 800 logger.js:112:17
[unknown:0] Initialized seel (Lvl 10) with 800 XP logger.js:112:17
[unknown:0] [CACHE_DEBUG] Applying standard spawn data for seel: logger.js:112:17
[unknown:0] [CACHE_DEBUG] - pokemonData.landuseData = null logger.js:112:17
[unknown:0] [CACHE_DEBUG] - spawn location = 50.49054966628726,10.853516788920754 logger.js:112:17
[unknown:0] [CACHE_DEBUG] ✗ No landuse data available for seel - this indicates cache lookup failed logger.js:112:17
[unknown:0] Created final Pokemon: seel (base: seel) Level 10, Dex #86 logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon bellsprout with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] [CACHE_DEBUG] Applying standard spawn data for bellsprout: logger.js:112:17
[unknown:0] [CACHE_DEBUG] - pokemonData.landuseData = null logger.js:112:17
[unknown:0] [CACHE_DEBUG] - spawn location = 50.49332510872419,10.853589044373644 logger.js:112:17
[unknown:0] [CACHE_DEBUG] ✗ No landuse data available for bellsprout - this indicates cache lookup failed logger.js:112:17
[unknown:0] Created final Pokemon: bellsprout (base: bellsprout) Level 1, Dex #69 logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon cubone with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized cubone (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] [CACHE_DEBUG] Applying standard spawn data for cubone: logger.js:112:17
[unknown:0] [CACHE_DEBUG] - pokemonData.landuseData = null logger.js:112:17
[unknown:0] [CACHE_DEBUG] - spawn location = 50.49144556208901,10.860556101502462 logger.js:112:17
[unknown:0] [CACHE_DEBUG] ✗ No landuse data available for cubone - this indicates cache lookup failed logger.js:112:17
[unknown:0] Created final Pokemon: cubone (base: cubone) Level 5, Dex #104 logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] Creating new Pokemon nidoran-m with dex_number: null (from evolutionData.dex: undefined) logger.js:112:17
[unknown:0] Initial XP for level 5 (common): 100 logger.js:112:17
[unknown:0] Initialized nidoran-m (Lvl 5) with 100 XP logger.js:112:17
[unknown:0] [CACHE_DEBUG] Applying standard spawn data for nidoran-m: logger.js:112:17
[unknown:0] [CACHE_DEBUG] - pokemonData.landuseData = null logger.js:112:17
[unknown:0] [CACHE_DEBUG] - spawn location = 50.493553486746166,10.84952281906951 logger.js:112:17
[unknown:0] [CACHE_DEBUG] ✗ No landuse data available for nidoran-m - this indicates cache lookup failed logger.js:112:17
[unknown:0] Created final Pokemon: nidoran-m (base: nidoran-m) Level 5, Dex #32 logger.js:112:17
[unknown:0] Storage sync disabled - skipping sync after Pokemon update logger.js:112:17
[unknown:0] [LANDUSE_STATS] 📊 Standard spawn results with 1km landuse coverage: logger.js:138:17
[unknown:0] [LANDUSE_STATS] 🎯 Total spawn attempts: 27 logger.js:138:17
[unknown:0] [LANDUSE_STATS] ✅ Successful landuse spawns: 1/20 (5%) logger.js:138:17
[unknown:0] [LANDUSE_STATS] ⚪ Random spawns without landuse: 26/20 (130%) logger.js:138:17
[unknown:0] [LANDUSE_STATS] ⚡ Average attempts per spawn: 1.4 logger.js:138:17
[unknown:0] [LANDUSE_STATS] 🗺️ Coverage area: 1km radius (3.141592653589793 km²) logger.js:138:17
[unknown:0] [STANDARD_SPAWN] ✅ Successfully created 20 standard Pokemon with comprehensive landuse coverage logger.js:138:17
[unknown:0] [CACHE_DEBUG] Spawned 20 standard + 20 landuse Pokemon logger.js:112:17
[unknown:0] Re-enabled storage sync after reset logger.js:112:17
[unknown:0] Spawn reset complete. New Pokemon count: 40 logger.js:112:17
[unknown:0] Browser Geolocation update: lat=50.4922112, lng=10.8560384, accuracy=1219.9206785921297 logger.js:112:17
[unknown:0] Updating player sprite at 50.4922112, 10.8560384, heading: 0 logger.js:112:17
[unknown:0] Player is static, distSinceLastMove: 0 logger.js:112:17
[unknown:0] Browser Geolocation update: lat=50.4922112, lng=10.8560384, accuracy=1219.9206785921297 logger.js:112:17
[unknown:0] Updating player sprite at 50.4922112, 10.8560384, heading: 0 logger.js:112:17
[unknown:0] Player is static, distSinceLastMove: 0 logger.js:112:17
[unknown:0] Browser Geolocation update: lat=50.4922112, lng=10.8560384, accuracy=1219.9206785921297 logger.js:112:17
[unknown:0] Updating player sprite at 50.4922112, 10.8560384, heading: 0 logger.js:112:17
[unknown:0] Player is static, distSinceLastMove: 0 logger.js:112:17
[unknown:0] [POKEMON_DEBUG] {"id":"mdptjirjgzpsyqi","base_name":"golem","name":"geodude","level":5,"dex_number":74,"types":["rock","ground"],"landuse":{"special":true,"type":"cliff","typeName":"cliff","featureId":490688334},"currentForm":{}} logger.js:112:17
[unknown:0] [LANDUSE_INFO] Pokemon geodude: special=true, type=cliff, typeName=cliff logger.js:112:17
[unknown:0] Display form for geodude: {"name":"geodude","sprite":"./src/PokemonSprites/74.png","dex_number":74,"types":["rock","ground"],"evolution_chain_id":31} logger.js:112:17
[unknown:0] [POKEMON_DEBUG] {"id":"mdptjirjuoooui5","base_name":"vileplume","name":"oddish","level":1,"dex_number":43,"types":["grass","poison"],"landuse":{"special":true,"type":"wood","typeName":"wood","featureId":331590612},"currentForm":{}} logger.js:112:17
[unknown:0] [LANDUSE_INFO] Pokemon oddish: special=true, type=wood, typeName=wood logger.js:112:17
[unknown:0] Display form for oddish: {"name":"oddish","sprite":"./src/PokemonSprites/43.png","dex_number":43,"types":["grass","poison"],"evolution_chain_id":18} logger.js:112:17
[unknown:0] Browser Geolocation update: lat=50.4922112, lng=10.8560384, accuracy=1219.9206785921297 logger.js:112:17
[unknown:0] Updating player sprite at 50.4922112, 10.8560384, heading: 0 logger.js:112:17
[unknown:0] Player is static, distSinceLastMove: 0 logger.js:112:17
[unknown:0] [POKEMON_DEBUG] {"id":"mdptjirsyvoob0h","base_name":"seel","name":"seel","level":10,"dex_number":86,"types":["water"],"landuse":{"special":false,"type":null,"typeName":null,"featureId":null},"currentForm":{}} logger.js:112:17
[unknown:0] [LANDUSE_INFO] Pokemon seel: special=false, type=null, typeName=null logger.js:112:17
[unknown:0] Display form for seel: {"name":"seel","sprite":"./src/PokemonSprites/86.png","dex_number":86,"types":["water"],"evolution_chain_id":37} logger.js:112:17
[unknown:0] [POKEMON_DEBUG] {"id":"mdptjirqjxt4pyv","base_name":"caterpie","name":"caterpie","level":5,"dex_number":10,"types":["bug"],"landuse":{"special":false,"type":null,"typeName":null,"featureId":null},"currentForm":{}} logger.js:112:17
[unknown:0] [LANDUSE_INFO] Pokemon caterpie: special=false, type=null, typeName=null logger.js:112:17
[unknown:0] Display form for caterpie: {"name":"caterpie","sprite":"./src/PokemonSprites/10.png","dex_number":10,"types":["bug"],"evolution_chain_id":4} logger.js:112:17
[unknown:0] [POKEMON_DEBUG] {"id":"mdptjirre8icxli","base_name":"cubone","name":"cubone","level":6,"dex_number":104,"types":["ground"],"landuse":{"special":false,"type":null,"typeName":null,"featureId":null},"currentForm":{}} logger.js:112:17
[unknown:0] [LANDUSE_INFO] Pokemon cubone: special=false, type=null, typeName=null logger.js:112:17
[unknown:0] Display form for cubone: {"name":"cubone","sprite":"./src/PokemonSprites/104.png","dex_number":104,"types":["ground"],"evolution_chain_id":46} logger.js:112:17
[unknown:0] Browser Geolocation update: lat=50.4922112, lng=10.8560384, accuracy=1219.9206785921297 logger.js:112:17
[unknown:0] Updating player sprite at 50.4922112, 10.8560384, heading: 0 logger.js:112:17
[unknown:0] Player is static, distSinceLastMove: 0 logger.js:112:17
[unknown:0] [POKEMON_DEBUG] {"id":"mdptjirqitgzk0c","base_name":"bellsprout","name":"bellsprout","level":7,"dex_number":69,"types":["grass","poison"],"landuse":{"special":false,"type":null,"typeName":null,"featureId":null},"currentForm":{}} logger.js:112:17
[unknown:0] [LANDUSE_INFO] Pokemon bellsprout: special=false, type=null, typeName=null logger.js:112:17
[unknown:0] Display form for bellsprout: {"name":"bellsprout","sprite":"./src/PokemonSprites/69.png","dex_number":69,"types":["grass","poison"],"evolution_chain_id":29} logger.js:112:17
[unknown:0] Browser Geolocation update: lat=50.4922112, lng=10.8560384, accuracy=1219.9206785921297 logger.js:112:17
[unknown:0] Updating player sprite at 50.4922112, 10.8560384, heading: 0 logger.js:112:17
[unknown:0] Player is static, distSinceLastMove: 0 logger.js:112:17
[unknown:0] Browser Geolocation update: lat=50.4922112, lng=10.8560384, accuracy=1219.9206785921297 logger.js:112:17
[unknown:0] Updating player sprite at 50.4922112, 10.8560384, heading: 0 logger.js:112:17
[unknown:0] Player is static, distSinceLastMove: 0 logger.js:112:17
[unknown:0] Browser Geolocation update: lat=50.4922112, lng=10.8560384, accuracy=1219.9206785921297 logger.js:112:17
[unknown:0] Updating player sprite at 50.4922112, 10.8560384, heading: 0 logger.js:112:17
[unknown:0] Player is static, distSinceLastMove: 0 logger.js:112:17
