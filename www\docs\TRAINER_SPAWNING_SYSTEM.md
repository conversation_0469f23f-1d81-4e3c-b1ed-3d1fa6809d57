# Advanced Trainer Spawning System

## Overview

The Advanced Trainer Spawning System replaces the simple single-trainer spawning with a sophisticated system that spawns 5 trainers on real OpenStreetMap (OSM) ways within a 500m radius. The system uses weighted sampling to prefer pedestrian paths over roads and implements intelligent fallback mechanisms for areas with sparse road networks.

## Key Features

### 🗺️ OSM-Constrained Positioning
- Spawns trainers on actual OSM ways, paths, and trails
- Excludes motorways and trunk roads for safety
- Prioritizes pedestrian and cycling infrastructure

### ⚖️ Weighted Sampling Algorithm
- **Foot paths (3x weight)**: footway, path, pedestrian, steps, hiking routes
- **Cycle paths (2x weight)**: cycleway, bicycle routes
- **Local roads (1x weight)**: residential, service, track, living_street
- **Primary roads (0.5x weight)**: primary, secondary, tertiary roads

### 📏 Intelligent Spacing
- 20m minimum spacing between trainers
- Automatic fallback to 10m spacing if insufficient positions
- Prevents trainer clustering

### 🔄 Respawn Triggers
- Respawns when player moves >250m from last spawn anchor
- Integrates with existing Pokemon spawning system
- Maintains separate spawn anchors for trainers and Pokemon

### 💾 Advanced Caching
- 60-minute cache duration for OSM data
- Separate cache namespace from landuse data
- localStorage persistence with memory fallback
- 200 entry cache limit with LRU eviction

### 🛡️ Robust Fallback System
- Graceful degradation when OSM data is unavailable
- Random positioning fallback with spacing enforcement
- Comprehensive error handling and logging

## Architecture

### Core Components

#### TrainerSpawner Class (`services/trainer-spawner.js`)
Main service class handling all trainer spawning logic:

```javascript
// Spawn 5 trainers using OSM-constrained positioning
const trainers = await trainerSpawner.spawnRandomTrainers(playerLat, playerLng, 5);
```

#### OSM Ways Cache System
Separate caching infrastructure for OSM ways data:
- Cache key format: `osm_ways_{lat}_{lng}_{radius}`
- 60-minute expiration with localStorage persistence
- Automatic cleanup of expired entries

#### Weighted Sampling Engine
Length-proportional sampling with tag-based weighting:
- Calculates cumulative distribution of weighted way lengths
- Samples points using random selection from distribution
- Validates positions and enforces spacing constraints

### Integration Points

#### GameState Integration
```javascript
// New property for tracking trainer spawn anchor
gameState.lastTrainerSpawnAnchor = {
  lat: playerLat,
  lng: playerLng,
  timestamp: Date.now()
};
```

#### Main.js Integration
```javascript
// Trainer spawning trigger in GPS handling
await handleTrainerSpawning(lat, lng);
```

#### Configuration Integration
```javascript
// New trainer configuration section
config.trainers = {
  spawnRadius: 500,
  spawnDistanceTrigger: 250,
  desiredCount: 5,
  minSpacing: 20,
  minSpacingFallback: 10,
  cacheTimeoutMinutes: 60
};
```

## API Reference

### Core Methods

#### `spawnRandomTrainers(playerLat, playerLng, count)`
Main method for spawning multiple trainers.

**Parameters:**
- `playerLat` (number): Player's latitude
- `playerLng` (number): Player's longitude  
- `count` (number): Number of trainers to spawn (default: 5)

**Returns:** `Promise<Array<Trainer>>` - Array of spawned trainers

#### `generateOSMConstrainedPositions(centerLat, centerLng, count, radius, minSpacing)`
Generates positions constrained to OSM ways.

**Parameters:**
- `centerLat` (number): Center latitude
- `centerLng` (number): Center longitude
- `count` (number): Number of positions (default: 5)
- `radius` (number): Search radius in meters (default: 500)
- `minSpacing` (number): Minimum spacing in meters (default: 20)

**Returns:** `Promise<Array<Object>>` - Array of position objects

#### `setTrainerTagWeights(config)`
Configure tag-based weighting for OSM ways.

**Parameters:**
- `config` (Object): Weight configuration
  - `footPriority` (number): Weight for foot paths (default: 3)
  - `cyclePriority` (number): Weight for cycle paths (default: 2)
  - `localRoadPriority` (number): Weight for local roads (default: 1)
  - `primaryRoadPriority` (number): Weight for primary roads (default: 0.5)

**Returns:** `Object` - Applied weight configuration

### Utility Methods

#### `fetchOSMWays(lat, lng, radius)`
Fetches OSM ways from Overpass API with caching and retry logic.

#### `toLineStrings(overpassJson)`
Converts Overpass JSON response to GeoJSON LineString features.

#### `samplePointsOnLines(lineStrings, neededCount, minSpacingMeters, ...)`
Performs weighted sampling on LineString features.

#### `validatePosition(position, centerLat, centerLng, radiusMeters, lineStrings)`
Validates and optionally snaps positions to nearest OSM way.

## Configuration

### Trainer Configuration (`config.js`)
```javascript
trainers: {
  spawnRadius: 500,           // Spawn radius in meters
  spawnDistanceTrigger: 250,  // Movement trigger distance
  desiredCount: 5,            // Number of trainers to spawn
  minSpacing: 20,             // Minimum spacing between trainers
  minSpacingFallback: 10,     // Fallback minimum spacing
  cacheTimeoutMinutes: 60     // Cache timeout in minutes
}
```

### Tag Weight Configuration
```javascript
// Configure weighting for different OSM way types
trainerSpawner.setTrainerTagWeights({
  footPriority: 3,      // Footways, paths, pedestrian areas
  cyclePriority: 2,     // Cycleways, bicycle routes
  localRoadPriority: 1, // Residential, service roads
  primaryRoadPriority: 0.5 // Primary, secondary roads
});
```

## Overpass Query

The system uses the following Overpass QL query to fetch suitable ways:

```overpass
[out:json][timeout:25];
(
  way(around:500,{lat},{lng})["highway"]["highway"!="motorway"]["highway"!="trunk"];
  way(around:500,{lat},{lng})["highway"~"path|footway|pedestrian|residential|service|track|living_street|steps"];
  way(around:500,{lat},{lng})["cycleway"];
  relation(around:500,{lat},{lng})["type"="route"]["route"~"hiking|foot|bicycle"];
);
out geom;
```

## Error Handling & Fallbacks

### Fallback Hierarchy
1. **OSM Ways Available**: Use weighted sampling on actual ways
2. **Insufficient OSM Ways**: Reduce minimum spacing from 20m to 10m
3. **Still Insufficient**: Fill remaining positions with random placement
4. **OSM API Failure**: Use complete random positioning with spacing enforcement

### Error Scenarios
- **Network Failures**: Exponential backoff retry (300ms, 900ms)
- **Invalid OSM Data**: Skip invalid elements, continue processing
- **Geometry Errors**: Log warnings, use fallback positioning
- **Cache Failures**: Continue without caching, log warnings

## Performance Considerations

### Caching Strategy
- **Memory Cache**: Fast access for recent queries
- **localStorage Cache**: Persistence across sessions
- **Cache Invalidation**: 60-minute expiration with automatic cleanup

### Query Optimization
- **Radius Limiting**: 500m maximum to balance coverage and performance
- **Timeout Handling**: 25-second Overpass timeout with retry logic
- **Parallel Processing**: Concurrent data collection where possible

### Resource Management
- **Cache Size Limits**: 200 entries maximum with LRU eviction
- **Memory Cleanup**: Automatic removal of expired cache entries
- **Request Throttling**: Built-in rate limiting via caching

## Debugging & Monitoring

### Logging Categories
- `[OSM-WAYS-CACHE]`: Cache operations and performance
- `[OSM-WAYS]`: OSM data processing and conversion
- `[TRAINER-SPAWNER]`: High-level spawning operations
- `[TRAINER-SPAWNING]`: Integration with main game loop

### Debug Information
- Cache hit/miss ratios
- OSM query response times
- Sampling success rates
- Fallback usage statistics
- Spacing validation results

## Integration with Existing Systems

### Pokemon Spawning Compatibility
- Separate spawn anchors prevent interference
- Independent caching systems avoid conflicts
- Parallel spawning operations for efficiency

### Trainer Battle System
- Maintains compatibility with existing Trainer class
- Preserves team generation and evolution systems
- No changes required to battle mechanics

### Map Rendering
- Uses existing trainer marker rendering
- Supports multiple trainers on map simultaneously
- Maintains popup and interaction functionality

## Future Enhancements

### Potential Improvements
- **Intersection Avoidance**: Avoid spawning near road intersections
- **Terrain Awareness**: Consider elevation and terrain difficulty
- **Time-Based Spawning**: Different trainer types based on time of day
- **Weather Integration**: Spawn preferences based on weather conditions
- **Player Behavior**: Adapt spawning based on player movement patterns

### Scalability Considerations
- **Regional Caching**: Cache larger areas for better coverage
- **Predictive Loading**: Pre-load OSM data for likely movement directions
- **Distributed Caching**: Share cache data between players
- **Server-Side Processing**: Move complex calculations to backend

## Troubleshooting

### Common Issues

#### No Trainers Spawning
1. Check network connectivity for Overpass API
2. Verify GPS permissions and location accuracy
3. Check console for error messages
4. Ensure player has moved >250m from last spawn

#### Trainers Spawning in Wrong Locations
1. Verify OSM data quality in the area
2. Check tag weight configuration
3. Review fallback positioning logs
4. Validate coordinate system usage (lat/lng vs lng/lat)

#### Performance Issues
1. Monitor cache hit rates
2. Check Overpass API response times
3. Review memory usage and cleanup
4. Consider reducing spawn radius or count

### Debug Commands
```javascript
// Check cache statistics
console.log(trainerSpawner.getCacheStats());

// Clear OSM ways cache
trainerSpawner.clearOSMWaysCache();

// Test spawning at specific location
await trainerSpawner.spawnRandomTrainers(51.96, 7.62, 5);
```

## Conclusion

The Advanced Trainer Spawning System provides a robust, scalable solution for spawning trainers on real-world infrastructure. By leveraging OpenStreetMap data and implementing intelligent fallback mechanisms, it creates a more immersive and realistic gameplay experience while maintaining excellent performance and reliability.

The system's modular architecture ensures easy maintenance and future enhancements, while comprehensive error handling and logging facilitate debugging and monitoring in production environments.
