// utils/pokedex-data-manager.js
// Centralized Pokedex data loading to eliminate duplication

import { logger } from './logger.js';

/**
 * Pokedex Data Manager - Singleton for loading and caching pokedex data
 */
export class PokedexDataManager {
  static instance = null;
  static data = null;
  static loading = false;
  static loadPromise = null;

  /**
   * Get pokedex data (loads if not already loaded)
   * @returns {Promise<Array>} - The pokedex data array
   */
  static async getPokedexData() {
    // If data is already loaded, return it
    if (this.data) {
      return this.data;
    }

    // If already loading, wait for the existing promise
    if (this.loading && this.loadPromise) {
      return await this.loadPromise;
    }

    // Start loading
    this.loading = true;
    this.loadPromise = this.loadPokedexDataInternal();

    try {
      this.data = await this.loadPromise;
      return this.data;
    } finally {
      this.loading = false;
      this.loadPromise = null;
    }
  }

  /**
   * Internal method to load pokedex data from file
   * @returns {Promise<Array>} - The pokedex data array
   * @private
   */
  static async loadPokedexDataInternal() {
    try {
      logger.debug('Loading pokedex data from file...');
      const response = await fetch('./pokedex-151.json');
      
      if (!response.ok) {
        throw new Error(`Failed to load pokedex data: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      
      if (!Array.isArray(data) || data.length === 0) {
        throw new Error('Invalid pokedex data format');
      }

      logger.info(`Successfully loaded ${data.length} Pokemon from pokedex data`);
      return data;
    } catch (error) {
      logger.error('Error loading pokedex data:', error);
      
      // Return minimal fallback data for testing
      const fallbackData = [
        {
          "name": "Pikachu",
          "de": "Pikachu",
          "dex_number": 25,
          "types": ["electric"],
          "rarity": "starter",
          "image_url": "./src/PokemonSprites/25.png",
          "evolution_level": 30,
          "evolution_chain_id": 10
        },
        {
          "name": "Raichu",
          "de": "Raichu",
          "dex_number": 26,
          "types": ["electric"],
          "rarity": "starter",
          "image_url": "./src/PokemonSprites/26.png",
          "evolution_chain_id": 10
        }
      ];
      
      logger.warn('Using fallback pokedex data for testing');
      return fallbackData;
    }
  }

  /**
   * Force reload pokedex data (useful for testing)
   * @returns {Promise<Array>} - The pokedex data array
   */
  static async reloadPokedexData() {
    this.data = null;
    this.loading = false;
    this.loadPromise = null;
    return await this.getPokedexData();
  }

  /**
   * Get cached data without loading (returns null if not loaded)
   * @returns {Array|null} - The cached pokedex data or null
   */
  static getCachedData() {
    return this.data;
  }

  /**
   * Check if data is currently loading
   * @returns {boolean} - Whether data is being loaded
   */
  static isLoading() {
    return this.loading;
  }
}
