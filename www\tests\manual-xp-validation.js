// manual-xp-validation.js
// Manual validation of XP calculation consistency

// Simulate the functions we need for testing
function getExpForLevel(level, curve) {
  switch (curve) {
    case 'fast':
      return Math.floor((4 * Math.pow(level, 3)) / 5);
    case 'medium_fast':
      return Math.pow(level, 3);
    case 'medium_slow':
      return Math.floor((6 / 5) * Math.pow(level, 3) - 15 * Math.pow(level, 2) + 100 * level - 140);
    case 'slow':
      return Math.floor((5 * Math.pow(level, 3)) / 4);
    case 'parabolic':
      return Math.floor(1.2 * Math.pow(level, 3) - 10 * Math.pow(level, 2) + 90 * level);
    default:
      return Math.pow(level, 3);
  }
}

function getExpCurveForRarity(rarity) {
  const curves = {
    mythical: 'slow',
    legendary: 'medium_slow',
    starter: 'medium_fast',
    rare: 'parabolic',
    scarce: 'fast',
    common: 'fast'
  };
  return curves[rarity] || 'fast';
}

// The unified calculateExpProgress function (from battle-utils.js)
function calculateExpProgress(pokemon, additionalExp = 0) {
  try {
    const result = {
      currentExp: 0,
      currentLevelExp: 0,
      nextLevelExp: 0,
      expInCurrentLevel: 0,
      expNeededForNextLevel: 0,
      progressPercentage: 0,
      newExpPercentage: 0,
      willLevelUp: false
    };

    if (!pokemon || typeof pokemon.level !== 'number') {
      return result;
    }

    const level = pokemon.level || 1;
    const nextLevel = level + 1;
    const rarity = pokemon.rarity || 'common';
    const curve = getExpCurveForRarity(rarity);

    const currentLevelExp = getExpForLevel(level, curve);
    const nextLevelExp = getExpForLevel(nextLevel, curve);
    const currentExp = typeof pokemon.experience === 'number' ? pokemon.experience : currentLevelExp;

    const expInCurrentLevel = currentExp - currentLevelExp;
    const expNeededForNextLevel = nextLevelExp - currentLevelExp;
    const progressPercentage = Math.min(100, Math.floor((expInCurrentLevel / expNeededForNextLevel) * 100));

    const newExp = currentExp + additionalExp;
    const newExpInLevel = Math.min(newExp - currentLevelExp, expNeededForNextLevel);
    const newProgressPercentage = Math.min(100, Math.floor((newExpInLevel / expNeededForNextLevel) * 100));
    const willLevelUp = newExp >= nextLevelExp;

    result.currentExp = currentExp;
    result.currentLevelExp = currentLevelExp;
    result.nextLevelExp = nextLevelExp;
    result.expInCurrentLevel = expInCurrentLevel;
    result.expNeededForNextLevel = expNeededForNextLevel;
    result.progressPercentage = progressPercentage;
    result.newExpPercentage = newProgressPercentage;
    result.willLevelUp = willLevelUp;

    return result;
  } catch (e) {
    console.error('Error in calculateExpProgress:', e);
    return {
      currentExp: 0,
      currentLevelExp: 0,
      nextLevelExp: 0,
      expInCurrentLevel: 0,
      expNeededForNextLevel: 0,
      progressPercentage: 0,
      newExpPercentage: 0,
      willLevelUp: false
    };
  }
}

// Test data
const testPokemon = [
  {
    name: 'Pikachu',
    level: 5,
    experience: 125,
    rarity: 'starter'
  },
  {
    name: 'Charmander',
    level: 10,
    experience: 1000,
    rarity: 'starter'
  },
  {
    name: 'Pidgey',
    level: 15,
    experience: 2700,
    rarity: 'common'
  }
];

// Run validation
console.log('=== Manual XP Calculation Validation ===\n');

testPokemon.forEach((pokemon, index) => {
  console.log(`Test ${index + 1}: ${pokemon.name} (Level ${pokemon.level}, ${pokemon.rarity})`);
  
  const result = calculateExpProgress(pokemon);
  
  console.log(`Current XP: ${result.currentExp}`);
  console.log(`Current Level XP: ${result.currentLevelExp}`);
  console.log(`Next Level XP: ${result.nextLevelExp}`);
  console.log(`XP in Current Level: ${result.expInCurrentLevel}`);
  console.log(`XP Needed for Next Level: ${result.expNeededForNextLevel}`);
  console.log(`Progress Percentage: ${result.progressPercentage}%`);
  
  // Test with additional XP
  const additionalXP = 100;
  const resultWithBonus = calculateExpProgress(pokemon, additionalXP);
  console.log(`With +${additionalXP} XP: ${resultWithBonus.newExpPercentage}% (Will Level Up: ${resultWithBonus.willLevelUp})`);
  
  console.log('---');
});

console.log('✅ Manual validation completed successfully!');
console.log('Both BattleScreen and TrainerBattleScreen now use the same calculateExpProgress function from battle-utils.js');
